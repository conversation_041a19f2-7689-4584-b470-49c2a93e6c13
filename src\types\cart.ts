/**
 * Cart and Address related TypeScript interfaces
 */

import { VendorDetails } from "./vendor";

export interface Address {
  id: number;
  billing_shipping: "1" | "2"; // "1" = Billing, "2" = Shipping
  contact_person_name: string;
  company_name: string | null;
  address_line_one: string;
  address_line_two: string | null;
  post_code: string;
  gst_number: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  country_id: number;
  state_id: number;
  city_id: number;
  user_id: number;
  user: {
    id: number;
    name: string;
    email: string;
  };
}

export interface CartItem {
  id: number;
  product: {
    id: number;
    title: string;
    main_image?: {
      url: string;
    };
  };
  quantity: number;
  price: string;
  product_id: number;
  availability?: 0 | 1; // 0 = out of stock, 1 = available
}

export interface CartSummaryData {
  sub_total?: string | number;
  cgst_total?: string | number;
  cgst_percentage?: string | number;
  sgst_total?: string | number;
  sgst_percentage?: string | number;
  igst_total?: string | number;
  igst_percentage?: string | number;
  total_amount?: string | number;
  cartItems: CartItem[];
}

export interface AddressCollection {
  all: Address[];
  billing: Address[];
  shipping: Address[];
}

export interface CartStepProps {
  cartList: CartSummaryData;
  onQuantityChange: (productId: number, newQuantity: number) => void;
  onRemoveItem: (itemId: number) => void;
  onClearCart: () => void;
  onContinue: () => void;
}

export interface AddressStepProps {
  addresses: Address[];
  selectedBillingId: number | null;
  selectedShippingId: number | null;
  useBillingAsShipping: boolean;
  onSelectBilling: (address: Address) => void;
  onSelectShipping: (address: Address) => void;
  onEditAddress: (addressId: number) => void;
  onToggleBillingAsShipping: (value: boolean) => void;
  onBack: () => void;
  onContinue: () => void;
  isReadyForCheckout: boolean;
}

export interface SummaryStepProps {
  cartData: CartSummaryData;
  billingAddress: Address | null;
  shippingAddress: Address | null;
  shipToSameAddress: boolean;
  onBack: () => void;
  onPlaceOrder: () => void;
  isPlacingOrder: boolean;
  isReadyForCheckout: boolean;
}

export enum CheckoutStep {
  CART = 1,
  VENDOR_SELECTION = 2,
  ADDRESS_PREVIEW = 3,
  PAYMENT = 4,
  SUMMARY = 5,
}

export interface OfferCartProduct {
  product: number;
  sub_total: string | number;
}

export interface OfferCartList {
  product: OfferCartProduct[];
  sub_total: string | number;
  total_amount: string | number;
  discount_amount: string | number;
}

export interface CartState {
  currentStep: CheckoutStep;
  selectedBillingAddressId: number | null;
  selectedShippingAddressId: number | null;
  useBillingAsShipping: boolean;
  isLoadingAddresses: boolean;
  showClearCartModal: boolean;
  isClearingCart: boolean;
  showOrderConfirmModal: boolean;
  isPlacingOrder: boolean;
}

export interface CartActions {
  // Navigation
  handleNextStep: () => void;
  handlePreviousStep: () => void;
  handleStepSelect: (stepId: number) => void;

  // Cart management
  handleQuantityChange: (productId: number, newQuantity: number) => void;
  handleRemoveItem: (itemId: number) => void;
  handleClearCart: () => void;
  confirmClearCart: () => Promise<void>;

  // Address management
  handleBillingAddressSelection: (address: Address) => void;
  handleShippingAddressSelection: (address: Address) => void;
  handleToggleBillingAsShipping: (value: boolean) => void;
  handleEditAddress: (
    addressId: number,
    selectedVendor?: VendorDetails
  ) => void;

  // Order management
  handlePlaceOrder: () => void;
  confirmPlaceOrder: () => Promise<void>;
}

export interface CartHelpers {
  getAddresses: () => AddressCollection;
  getEffectiveShippingAddressId: () => number | null;
  getSelectedBillingAddress: () => Address | null;
  getSelectedShippingAddress: () => Address | null;
  isReadyForCheckout: () => boolean;
}
