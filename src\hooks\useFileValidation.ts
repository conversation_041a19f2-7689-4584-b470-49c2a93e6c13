import { useCallback } from "react";
import { useAppSelector } from "@/store/store";
import { validateFile, FileValidationSettings } from "@/utils/fileValidation";
import { ImagePickerAsset } from "expo-image-picker";

export const useFileValidation = () => {
  const { settingsList } = useAppSelector((state) => state.auth);

  // Get file validation settings from backend
  const fileValidationSettings: FileValidationSettings | undefined =
    settingsList?.fix_settings;

  const validateImageFile = useCallback(
    async (asset: ImagePickerAsset) => {
      if (!fileValidationSettings) {
        return { isValid: false, error: "Validation settings not available" };
      }
      return await validateFile(asset, fileValidationSettings, "image");
    },
    [fileValidationSettings]
  );

  const validateGeneralFile = useCallback(
    async (asset: ImagePickerAsset) => {
      if (!fileValidationSettings) {
        return { isValid: false, error: "Validation settings not available" };
      }
      return await validateFile(asset, fileValidationSettings, "general");
    },
    [fileValidationSettings]
  );

  const getSupportedFormatsDisplay = useCallback(
    (fileType: "image" | "general" = "image") => {
      if (!fileValidationSettings) return "Settings not available";
      const formats = fileValidationSettings.supported_file_format[fileType]
        .split(",")
        .map((format) =>
          format.replace(/^(image|video|application|text)\//, "").toUpperCase()
        )
        .join(", ");
      return formats;
    },
    [fileValidationSettings]
  );

  const getMaxFileSize = useCallback(
    (fileType: "image" | "general" = "image") => {
      if (!fileValidationSettings) return 0;
      return fileValidationSettings.file_size[fileType];
    },
    [fileValidationSettings]
  );

  return {
    fileValidationSettings,
    validateImageFile,
    validateGeneralFile,
    getSupportedFormatsDisplay,
    getMaxFileSize,
  };
};
