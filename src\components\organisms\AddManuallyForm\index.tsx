import React, { useState, useEffect } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getCountryListAction } from "@/store/actions/auth";
import { CountryCode } from "@/types/auth";
import { useForm, FormProvider } from "react-hook-form";
import {
  InlineFormContainer,
  InlineFormHeader,
  InlineFormTitle,
  CloseInlineButton,
  InlineFormContent,
  InlineFormActions,
  CancelInlineButton,
  AddInlineButton,
} from "@/styles/Complaint.styles";
import PhoneInput from "@/components/molecules/PhoneInput";
import FormField from "@/components/molecules/FormField";
import Text from "@/components/atoms/Text";

interface AddManuallyFormProps {
  isVisible: boolean;
  onClose: () => void;
  onAdd: (
    mobileNumber: string,
    countryCode: CountryCode,
    firstName: string,
    lastName: string
  ) => void;
  entityType: "vendor" | "customer";
}

interface FormData {
  mobile: string;
  firstName: string;
  lastName: string;
}

const AddManuallyForm: React.FC<AddManuallyFormProps> = ({
  isVisible,
  onClose,
  onAdd,
  entityType,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const { countryList } = useAppSelector((state) => state.auth);
  const [countryCode, setCountryCode] = useState<CountryCode | null>(null);

  const methods = useForm<FormData>({
    defaultValues: {
      mobile: "",
      firstName: "",
      lastName: "",
    },
  });

  const { watch, setValue } = methods;
  const mobileNumber = watch("mobile");
  const firstName = watch("firstName");
  const lastName = watch("lastName");

  useEffect(() => {
    const fetchCountryList = async () => {
      if (!countryList) {
        try {
          const response = await dispatch(getCountryListAction({})).unwrap();
          if (response.status && response?.data) {
            setCountryCode(response?.data?.[0]);
          }
        } catch (error) {
          console.error("Failed to load country list:", error);
        }
      } else if (!countryCode) {
        setCountryCode(countryList[0]);
      }
    };
    fetchCountryList();
  }, [dispatch, countryList, countryCode]);

  const handleAdd = () => {
    if (
      countryCode &&
      mobileNumber &&
      String(mobileNumber).trim() &&
      firstName &&
      String(firstName).trim() &&
      lastName &&
      String(lastName).trim()
    ) {
      onAdd(
        String(mobileNumber).trim(),
        countryCode,
        String(firstName).trim(),
        String(lastName).trim()
      );
      setValue("mobile", "");
      setValue("firstName", "");
      setValue("lastName", "");
      onClose();
    }
  };

  const handleClose = () => {
    setValue("mobile", "");
    setValue("firstName", "");
    setValue("lastName", "");
    onClose();
  };

  const handleCountrySelect = (country: CountryCode) => {
    setCountryCode(country);
  };

  const isFormValid =
    mobileNumber &&
    String(mobileNumber).trim().length > 9 &&
    countryCode &&
    firstName &&
    String(firstName).trim().length > 0 &&
    lastName &&
    String(lastName).trim().length > 0;

  if (!isVisible) return null;

  return (
    <FormProvider {...methods}>
      <InlineFormContainer>
        <InlineFormHeader>
          <InlineFormTitle>
            {entityType === "vendor"
              ? t("complaint.form.add_vendor_manually")
              : t("complaint.form.add_customer_manually")}
          </InlineFormTitle>
          <CloseInlineButton onPress={handleClose}>
            <MaterialIcons name="close" size={24} color={theme.colors.text} />
          </CloseInlineButton>
        </InlineFormHeader>

        <InlineFormContent>
          <FormField
            name="firstName"
            label={t("common.first_name")}
            placeholder={t("complaint.form.enter_first_name")}
            rules={{ required: true }}
            style={{ marginBottom: 16 }}
          />

          <FormField
            name="lastName"
            label={t("common.last_name")}
            placeholder={t("complaint.form.enter_last_name")}
            rules={{ required: true }}
            style={{ marginBottom: 16 }}
          />

          <PhoneInput
            label={t("phone_number")}
            name="mobile"
            placeholder={t("complaint.form.enter_mobile_number")}
            countryCode={countryCode}
            onCountrySelect={handleCountrySelect}
            countryList={countryList || []}
            rules={{ required: true }}
          />

          <InlineFormActions>
            <CancelInlineButton onPress={handleClose}>
              <Text>{t("common.cancel")}</Text>
            </CancelInlineButton>
            <AddInlineButton disabled={!isFormValid} onPress={handleAdd}>
              <Text
                style={{
                  color: isFormValid ? theme.colors.white : theme.colors.text,
                }}
              >
                {t("complaint.add_manually")}
              </Text>
            </AddInlineButton>
          </InlineFormActions>
        </InlineFormContent>
      </InlineFormContainer>
    </FormProvider>
  );
};

export default AddManuallyForm;
