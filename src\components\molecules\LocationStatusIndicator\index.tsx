import React from "react";
import { useTranslation } from "react-i18next";
import {
  LocationStatusContainer,
  LocationStatusText,
  LocationErrorContainer,
  LocationErrorText,
  RetryButton,
  RetryButtonText,
  LocationSuccessContainer,
  LocationSuccessText,
} from "@/styles/Complaint.styles";

interface LocationStatusIndicatorProps {
  isLoading: boolean;
  error: string | null;
  coordinates: { latitude: number; longitude: number } | null;
  onRetry: () => void;
}

export const LocationStatusIndicator: React.FC<
  LocationStatusIndicatorProps
> = ({ isLoading, error, coordinates, onRetry }) => {
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <LocationStatusContainer>
        <LocationStatusText>
          📍{" "}
          {t("complaint.location.getting_location", "Getting your location...")}
        </LocationStatusText>
      </LocationStatusContainer>
    );
  }

  if (error) {
    return (
      <LocationErrorContainer>
        <LocationErrorText>❌ {error}</LocationErrorText>
        <RetryButton onPress={onRetry}>
          <RetryButtonText>{t("common.retry", "Retry")}</RetryButtonText>
        </RetryButton>
      </LocationErrorContainer>
    );
  }

  return null;
};
