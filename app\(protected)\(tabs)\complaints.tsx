import React, { useEffect, useState, useCallback } from "react";
import { useFocusEffect, useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import {
  getComplaintsListAction,
  getComplaintStatusAction,
  getComplaintIssueListAction,
  getPartListAction,
} from "@/store/actions/complaint";
import {
  Container,
  Content,
  ErrorText,
  EmptyContainer,
  EmptyText,
  FloatingActionButton,
  Header,
  SortInfoRow,
  ItemsCountText,
  SortButtonRow,
  SortLabel,
  SortValueText,
  ActiveFiltersContainer,
  FilterChipsRow,
  ActiveFilterChip,
  ActiveFilterText,
} from "@/styles/Complaint.styles";
import { Ionicons } from "@expo/vector-icons";
import { LoadingOverlay, SearchBar, ComplaintCard } from "@/components";
import { useTheme } from "@/hooks/useTheme";
import { FlatList } from "react-native";
import { getCustomerListAction } from "@/store/actions/customer";
import { UserType } from "@/types/api";
import { ComplaintListPayload } from "@/types/complaint";
import { useDebounce } from "@/utils/useDebounce";
import { useRef } from "react";
import {
  complaintSortOptions,
  complaintStatusList,
  complaintTypeOptions,
} from "@/utils/common";
import { SortOptionList } from "@/components/organisms/SortBottomSheet";
import SortBottomSheet from "@/components/organisms/SortBottomSheet";
import StatusFilterBottomSheet from "@/components/organisms/StatusFilterBottomSheet";
import { setFilters } from "@/store/slices/complaintSlice";
import Toast from "react-native-toast-message";

const ComplaintsScreen = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { theme } = useTheme();

  // Redux state
  const {
    complaints = [],
    complaintStatus,
    complaintsLoading,
    isLoadingMore,
    filters,
    current_page,
    last_page,
  } = useAppSelector((state: RootState) => state.complaints);

  const { user } = useAppSelector((state: RootState) => state.auth);
  const isServicePerson = user?.role_id === UserType.SERVICEPERSON;

  // Local state
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 300);
  const [selectedSort, setSelectedSort] = useState<SortOptionList | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [selectedComplaintType, setSelectedComplaintType] = useState<
    number | null
  >(null);
  const [sortSheetVisible, setSortSheetVisible] = useState(false);
  const [statusSheetVisible, setStatusSheetVisible] = useState(false);

  // Fetch complaints list from API
  const fetchComplaints = useCallback(async (params: ComplaintListPayload) => {
    try {
      if (params.page === 1) setIsRefreshing(true);
      await dispatch(getComplaintsListAction(params)).unwrap();
    } catch (error: any) {
      console.error(error);
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to load complaints",
      });
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  useEffect(() => {
    const initializeMasterData = async () => {
      try {
        if (user?.role_id === UserType.SERVICEPERSON) {
          await dispatch(getPartListAction({})).unwrap();
        }
        await dispatch(getComplaintStatusAction({})).unwrap();
        await dispatch(getComplaintIssueListAction({})).unwrap();
        if (user?.role_id === UserType.SALESPERSON) {
          await dispatch(getCustomerListAction({})).unwrap();
        }
      } catch (error: any) {
        Toast.show({
          type: "error",
          text1: error?.message || "Failed to load master data",
        });
      }
    };

    initializeMasterData();
  }, []);

  useFocusEffect(
    useCallback(() => {
      const initializeData = async () => {
        try {
          await handleRefresh();
        } catch (err) {
          console.log(err);
        }
      };

      initializeData();
    }, [])
  );

  const handleRefresh = useCallback(async () => {
    await fetchComplaints({
      page: 1,
    });
    setSelectedSort(null);
    setSelectedStatus(null);
    setSelectedComplaintType(null);
  }, [fetchComplaints]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && current_page < last_page) {
      fetchComplaints({
        page: current_page + 1,
        order_by: selectedSort?.order_by,
        sort_order: selectedSort?.sort_order,
      });
    }
  }, [isLoadingMore, current_page, last_page, fetchComplaints, selectedSort]);

  const handleSearch = useCallback(
    async (text: string) => {
      await fetchComplaints({
        page: 1,
        search: text,
      });
    },
    [fetchComplaints]
  );

  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length > 3) {
      handleSearch(debouncedSearch);
    } else if (debouncedSearch === "") {
      // Reset search when cleared
      handleSearch("");
    }
  }, [debouncedSearch]);

  const handleSortPress = () => setSortSheetVisible(true);
  const handleStatusPress = () => setStatusSheetVisible(true);

  const handleSortClear = () => {
    setSelectedSort(null);
    setSortSheetVisible(false);
    fetchComplaints({
      page: 1,
      order_by: null,
      sort_order: null,
    });
  };

  const handleSortDismiss = () => setSortSheetVisible(false);

  const handleSortApply = (sortType: SortOptionList | null) => {
    setSelectedSort(sortType);
    setSortSheetVisible(false);
    fetchComplaints({
      page: 1,
      order_by: sortType?.order_by,
      sort_order: sortType?.sort_order,
    });
  };

  const handleStatusClear = () => {
    handleStatusApply(null, null);
  };

  const handleStatusApply = async (
    statusId: number | null,
    complaintTypeId: number | null
  ) => {
    const payload: ComplaintListPayload = {
      page: 1,
    };
    if (selectedSort?.order_by) {
      payload.order_by = selectedSort.order_by;
    }
    if (selectedSort?.sort_order) {
      payload.sort_order = selectedSort.sort_order;
    }
    if (statusId) payload.status = statusId;
    if (complaintTypeId) payload.complaint_type = complaintTypeId;
    await dispatch(setFilters({ ...filters, ...payload }));
    setSelectedStatus(statusId);
    setSelectedComplaintType(complaintTypeId);
    setStatusSheetVisible(false);
    await fetchComplaints(payload);
  };

  const handleStatusDismiss = () => setStatusSheetVisible(false);

  const getStatusLabel = (state: number) => {
    return complaintStatus.find((status) => status.id === Number(state))?.label;
  };

  if (
    complaintsLoading &&
    !isRefreshing &&
    !isLoadingMore &&
    current_page === 1
  ) {
    return <LoadingOverlay isLoading={complaintsLoading} />;
  }

  const handleComplaintPress = (complaint: any) => {
    router.push(`/complaint/${complaint.id}`);
  };

  const renderActiveFilters = () => {
    const activeFilters = [];

    if (selectedStatus) {
      const status = complaintStatusList.find((s) => s.id === selectedStatus);
      if (status) {
        activeFilters.push({
          label: status.label,
          onRemove: () => handleStatusApply(null, selectedComplaintType),
        });
      }
    }

    if (selectedComplaintType) {
      const complaintType = complaintTypeOptions.find(
        (c) => c.id === selectedComplaintType
      );
      if (complaintType) {
        activeFilters.push({
          label: complaintType.label,
          onRemove: () => handleStatusApply(selectedStatus, null),
        });
      }
    }

    if (activeFilters.length === 0) return null;

    return (
      <ActiveFiltersContainer>
        <FilterChipsRow>
          {activeFilters.map((filter, index) => (
            <ActiveFilterChip key={index} onPress={filter.onRemove}>
              <ActiveFilterText>{filter.label}</ActiveFilterText>
              <Ionicons name="close" size={14} color={theme.colors.white} />
            </ActiveFilterChip>
          ))}
        </FilterChipsRow>
      </ActiveFiltersContainer>
    );
  };

  const renderComplaintItem = ({ item: complaint }: { item: any }) => {
    return (
      <ComplaintCard
        complaint={complaint}
        getStatusLabel={getStatusLabel}
        onPress={handleComplaintPress}
      />
    );
  };

  return (
    <Container>
      <Header>
        <SearchBar
          placeholder={t("common.search_complaint")}
          onSearch={setSearch}
          onFilterPress={handleStatusPress}
          value={search}
        />
      </Header>

      {/* Info Row */}
      <SortInfoRow>
        <ItemsCountText>{complaints?.length || 0} items</ItemsCountText>
        <SortButtonRow hitSlop={20} onPress={handleSortPress}>
          <SortLabel>{t("common.sort_by")} : </SortLabel>
          <SortValueText>
            {selectedSort?.label || t("common.default")}
          </SortValueText>
          <Ionicons
            name="chevron-down"
            size={16}
            color={theme.colors.primary}
          />
        </SortButtonRow>
      </SortInfoRow>

      {renderActiveFilters()}

      <Content>
        <FlatList
          data={complaints || []}
          renderItem={renderComplaintItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 24 }}
          onRefresh={handleRefresh}
          refreshing={isRefreshing}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            isLoadingMore ? <LoadingOverlay isLoading /> : null
          }
          ListEmptyComponent={
            <EmptyContainer>
              <EmptyText>{t("complaint.no_complaints")}</EmptyText>
            </EmptyContainer>
          }
        />
      </Content>

      {/* Sort Bottom Sheet */}
      <SortBottomSheet
        isVisible={sortSheetVisible}
        onClose={handleSortDismiss}
        onApply={handleSortApply}
        onDismiss={handleSortDismiss}
        onClear={handleSortClear}
        sortOptions={complaintSortOptions}
        selectedOptions={selectedSort}
      />

      {/* Status Filter Bottom Sheet */}
      <StatusFilterBottomSheet
        filters={filters}
        isVisible={statusSheetVisible}
        onDismiss={handleStatusDismiss}
        statusOptions={complaintStatusList}
        orderTypeOptions={complaintTypeOptions}
        onClear={handleStatusClear}
        onApply={handleStatusApply}
        selectedkey="status"
        typeKey="complaint_type"
      />

      {!isServicePerson && (
        <FloatingActionButton
          onPress={() => {
            router.push("/complaint/new");
          }}
        >
          <Ionicons name="add" size={24} color={theme.colors.white} />
        </FloatingActionButton>
      )}
    </Container>
  );
};

export default ComplaintsScreen;
