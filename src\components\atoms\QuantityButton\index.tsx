import React from "react";
import { PressableProps } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { StyledButton } from "./styles";

interface QuantityButtonProps extends PressableProps {
  type: "increment" | "decrement";
  size?: number;
}

const QuantityButton: React.FC<QuantityButtonProps> = ({
  type,
  size = 20,
  disabled,
  ...props
}) => {
  const { theme } = useTheme();

  const iconName = type === "increment" ? "add" : "remove";

  return (
    <StyledButton
      disabled={disabled}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      {...props}
    >
      <Ionicons
        name={iconName}
        size={size}
        color={disabled ? theme.colors.gray : theme.colors.white}
      />
    </StyledButton>
  );
};

export default QuantityButton;
