import { styled } from "@/utils/styled";
import { TextInput, View, TouchableOpacity, Text } from "react-native";

export const Container = styled(View)`
  padding: 16px;
`;

export const Title = styled(Text)`
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 12px;
`;

export const SearchRow = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
`;

export const SearchInput = styled(TextInput)<{ borderColor: string }>`
  flex: 1;
  margin-left: 8px;
  border-bottom-width: 1px;
  border-color: ${(p) => p.borderColor};
  padding-vertical: 4px;
`;

export const Item = styled(TouchableOpacity)<{
  selected?: boolean;
  bgColor?: string;
  borderColor: string;
}>`
  padding: 16px;
  border-bottom-width: 1px;
  border-color: ${(p) => p.borderColor};
  background-color: ${(p) =>
    p.selected && p.bgColor ? p.bgColor : "transparent"};
`;

export const EmptyContainer = styled(View)`
  padding: 32px;
  align-items: center;
`;

export const Footer = styled(View)`
  padding-vertical: 30px;
`;

export const ConfirmBar = styled(View)<{
  borderColor: string;
  bgColor: string;
}>`
  padding: 16px;
  border-top-width: 1px;
  flex-direction: row;
  background-color: ${(p) => p.bgColor};
  align-items: center;
  justify-content: center;
  border-color: ${(p) => p.borderColor};
`;

export const ConfirmButton = styled(TouchableOpacity)<{
  bgColor: string;
  disabled?: boolean;
}>`
  padding: 14px;
  border-radius: 8px;
  align-items: center;
  flex: 1;
  background-color: ${(p) => p.bgColor};
  opacity: ${(p) => (p.disabled ? 0.6 : 1)};
`;

export const ConfirmButtonText = styled(Text)`
  color: #fff;
  font-weight: bold;
  text-align: center;
`;
