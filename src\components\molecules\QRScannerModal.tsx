import React, { useEffect, useState } from "react";
import { Modal, Text } from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import * as MediaLibrary from "expo-media-library";
import { useTheme } from "../../hooks/useTheme";
import { useTranslation } from "react-i18next";
import {
  Container,
  Centered,
  ScannerContainer,
  ScanFrame,
  Actions,
  ActionBtn,
  CloseBtn,
} from "../../styles/QRScannerModal.styles";
import { scanQrImage } from "@/utils/common";
import Toast from "react-native-toast-message";

interface QRScannerModalProps {
  visible: boolean;
  onClose: () => void;
  onScanned: (data: string) => void;
}

export const QRScannerModal: React.FC<QRScannerModalProps> = ({
  visible,
  onClose,
  onScanned,
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [permission, requestPermission] = useCameraPermissions();
  const [mediaPermission, requestMediaPermission] =
    MediaLibrary.usePermissions();
  const [scanned, setScanned] = useState(false);
  const [flash, setFlash] = useState(false);
  useEffect(() => {
    if (visible && !permission?.granted) {
      requestPermission();
    }
    if (visible && !mediaPermission?.granted) {
      requestMediaPermission();
    }
    setScanned(false);
  }, [visible]);

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (scanned) return; // prevent double scan
    setScanned(true);
    if (!!data) {
      const id = data.split("/qr/")[1];
      onScanned(id);
      onClose();
    }
  };

  const handleGalleryPress = async () => {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("common.no_gallery_access"),
        });
        onClose();
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const data = await scanQrImage(result.assets[0].uri);
        if (data) {
          handleBarCodeScanned({ data });
        }
      }
    } catch (error) {
      console.error("Error picking image:", error);
    }
  };

  const handleFlashToggle = () => {
    setFlash(!flash);
  };

  if (!permission) return null;
  if (!permission.granted) {
    return (
      <Modal visible={visible} transparent animationType="slide">
        <Centered>
          <Text style={{ color: theme.colors.white }}>
            {t("common.no_camera_access")}
          </Text>
          <CloseBtn onPress={onClose}>
            <Ionicons name="close" size={28} color={theme.colors.white} />
          </CloseBtn>
        </Centered>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} transparent animationType="slide">
      <Container>
        <CloseBtn onPress={onClose}>
          <Ionicons name="close" size={28} color={theme.colors.white} />
        </CloseBtn>
        <ScannerContainer>
          <CameraView
            style={{ position: "absolute", width: "100%", height: "100%" }}
            facing="back"
            flash={flash ? "on" : "off"}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ["qr"],
            }}
          />
          <ScanFrame />
        </ScannerContainer>
        <Actions>
          <ActionBtn onPress={handleGalleryPress}>
            <Ionicons
              name="image-outline"
              size={28}
              color={theme.colors.text}
            />
          </ActionBtn>
          <ActionBtn onPress={handleFlashToggle}>
            <Ionicons
              name={flash ? "flash" : "flash-off"}
              size={28}
              color={flash ? theme.colors.primary : theme.colors.text}
            />
          </ActionBtn>
        </Actions>
      </Container>
    </Modal>
  );
};
