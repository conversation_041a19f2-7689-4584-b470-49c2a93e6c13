import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetFlatList, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { VendorDetails, VendorListPayloadType } from "@/types/vendor";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  Title,
  SearchContainer,
  SearchInput,
  VendorItem,
  VendorInfo,
  VendorName,
  VendorId,
  EmptyContainer,
  EmptyText,
  StyledBottomSheetView,
  ItemsCountText,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getVendorListAction } from "@/store/actions/vendor";
import { View } from "react-native";
import { useDebounce } from "@/utils/useDebounce";

interface VendorSelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (vendor: VendorDetails) => void;
  vendors?: VendorDetails[];
  isLoading?: boolean;
}

const VendorSelectionBottomSheet: React.FC<VendorSelectionBottomSheetProps> = ({
  isVisible,
  onClose,
  onSelect,
  vendors: propVendors,
  isLoading: propIsLoading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use Redux state for pagination
  const {
    vendorList: reduxVendorList,
    isVendorListLoading,
    isLoadingMore,
    current_page,
    last_page,
  } = useAppSelector((state) => state.vendor);

  // Add debounced search
  const debouncedSearch = useDebounce(query, 300);

  // Use Redux state as primary, fallback to props if provided
  const vendors = Array.isArray(reduxVendorList)
    ? reduxVendorList
    : propVendors || [];
  const isLoading = isVendorListLoading || propIsLoading;

  // Fetch vendors function
  const fetchVendors = useCallback(
    async (params: VendorListPayloadType) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        await dispatch(getVendorListAction(params)).unwrap();
      } catch (error: any) {
        console.error("fetchVendors error:", error);
      } finally {
        setIsRefreshing(false);
      }
    },
    [dispatch]
  );

  // Load initial data when bottom sheet opens
  useEffect(() => {
    if (isVisible && vendors.length === 0) {
      fetchVendors({ page: 1 });
    }
  }, []);

  // Handle search
  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length >= 3) {
      fetchVendors({
        page: 1,
        search: debouncedSearch,
      });
    } else if (debouncedSearch === "") {
      fetchVendors({ page: 1 });
    }
  }, [debouncedSearch, fetchVendors]);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["70%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && current_page < last_page) {
      const payload: VendorListPayloadType = {
        page: current_page + 1,
      };
      if (debouncedSearch) payload.search = debouncedSearch;
      fetchVendors(payload);
    }
  }, [isLoadingMore, current_page, last_page, debouncedSearch, fetchVendors]);

  const handleVendorSelect = useCallback(
    (vendor: VendorDetails) => {
      onSelect(vendor);
      onClose();
    },
    [onSelect, onClose]
  );

  const renderVendorItem = useCallback(
    ({ item }: { item: VendorDetails }) => (
      <VendorItem onPress={() => handleVendorSelect(item)}>
        <VendorInfo>
          <VendorName>{item.first_name + " " + item.last_name}</VendorName>
          {item.email && <VendorId>{item.email}</VendorId>}
        </VendorInfo>
      </VendorItem>
    ),
    [handleVendorSelect]
  );

  const renderEmptyComponent = useCallback(() => {
    if (isLoading && !isRefreshing) {
      return <LoadingOverlay isLoading={true} size="large" />;
    }

    return (
      <EmptyContainer>
        <EmptyText>
          {query
            ? t("common.no_results_found")
            : t("common.no_vendors_available")}
        </EmptyText>
      </EmptyContainer>
    );
  }, [isLoading, isRefreshing, query, t]);

  const renderFooter = useCallback(() => {
    if (isLoadingMore) {
      return (
        <View style={{ paddingVertical: 30 }}>
          <LoadingOverlay isLoading={true} size="small" />
        </View>
      );
    }
    return null;
  }, [isLoadingMore]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enableDynamicSizing={false}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <Title>{t("cart.selectVendor")}</Title>

        <SearchContainer>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={t("cart.searchVendor")}
            value={query}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.text}
          />
        </SearchContainer>

        <ItemsCountText>
          {vendors.length} {t("vendorManagement.vendors")}
        </ItemsCountText>

        <BottomSheetFlatList
          data={vendors}
          keyExtractor={(item) =>
            item.vendor_id?.toString() || item.mobile?.toString() || item.email
          }
          renderItem={renderVendorItem}
          ListEmptyComponent={renderEmptyComponent}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          showsVerticalScrollIndicator={false}
          refreshing={isRefreshing}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default VendorSelectionBottomSheet;
