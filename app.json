{"expo": {"name": "Ozone-Batteries", "slug": "Ozone-Batteries", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/icon.png", "resizeMode": "contain", "backgroundColor": "#e3eaf6"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.trentiums.OzoneBatteries", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to location to provide you with nearby services and improve your experience.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to location to provide you with nearby services and improve your experience."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.trentiums.OzoneBatteries", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router"], "scheme": "ozone-batteries", "extra": {"router": {"origin": false}, "eas": {"projectId": "04f568c5-ae41-4036-aa2e-20f0ee772062"}}, "owner": "trentium-solution"}}