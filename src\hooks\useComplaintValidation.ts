import { useTranslation } from "react-i18next";
import Toast from "react-native-toast-message";
import { VendorDetails } from "@/types/vendor";
import { Customer } from "@/types/customer";

interface ValidationParams {
  complaintFor: "vendor" | "customer";
  selectedVendor?: VendorDetails;
  selectedCustomer?: Customer;
  selectedIssue: string;
  customDescription: string;
  batteryBrand: "ozone" | "other";
  selectedBillingAddressId: number | null;
  selectedShippingAddressId: number | null;
  useBillingAsShipping: boolean;
  watch: (field: string) => any;
}

export const useComplaintValidation = ({
  complaintFor,
  selectedVendor,
  selectedCustomer,
  selectedIssue,
  customDescription,
  batteryBrand,
  selectedBillingAddressId,
  selectedShippingAddressId,
  useBillingAsShipping,
  watch,
}: ValidationParams) => {
  const { t } = useTranslation();

  const validateStep1 = () => {
    if (!complaintFor) {
      Toast.show({
        type: "error",
        text1: t("error.title"),
        text2: t("complaint.validation.select_complaint_type"),
      });
      return false;
    }

    if (!selectedVendor && !selectedCustomer) {
      const currentMobileNumber =
        complaintFor === "vendor"
          ? watch("vendorMobileNumber")
          : watch("customerMobileNumber");
      const currentFirstName =
        complaintFor === "vendor"
          ? watch("vendorFirstName")
          : watch("customerFirstName");
      const currentLastName =
        complaintFor === "vendor"
          ? watch("vendorLastName")
          : watch("customerLastName");

      if (!currentMobileNumber || currentMobileNumber.trim().length === 0) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t(
            "complaint.validation.mobile_number_required_for_manual_entry"
          ),
        });
        return false;
      }

      if (!currentFirstName || currentFirstName.trim().length === 0) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("complaint.validation.first_name_required"),
        });
        return false;
      }

      if (!currentLastName || currentLastName.trim().length === 0) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("complaint.validation.last_name_required"),
        });
        return false;
      }
    }
    return true;
  };

  const validateStep2 = () => {
    const hasIssue = !!selectedIssue;
    const hasDescription = !!customDescription?.trim();

    if (!hasIssue && !hasDescription) {
      Toast.show({
        type: "error",
        text1: t("error.title"),
        text2: t("complaint.validation.issue_or_description_required"),
      });
      return false;
    }

    if (selectedIssue === "other" && !hasDescription) {
      Toast.show({
        type: "error",
        text1: t("error.title"),
        text2: t("complaint.validation.custom_description_required"),
      });
      return false;
    }

    if (batteryBrand === "ozone") {
      if (!watch("serialNumber") && !watch("qrCode")) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("complaint.validation.provide_serial_or_qr"),
        });
        return false;
      }
    } else {
      if (!watch("productImages") || watch("productImages").length === 0) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("complaint.validation.product_image_required"),
        });
        return false;
      }

      if (!watch("brandName")) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("complaint.validation.brand_name_required"),
        });
        return false;
      }
    }
    return true;
  };

  const validateFinalSubmission = () => {
    if (!selectedBillingAddressId) {
      Toast.show({
        type: "error",
        text1: t("error.title"),
        text2: t("complaint.validation.billing_address_required"),
      });
      return false;
    }

    if (!useBillingAsShipping && !selectedShippingAddressId) {
      Toast.show({
        type: "error",
        text1: t("error.title"),
        text2: t("complaint.validation.shipping_address_required"),
      });
      return false;
    }
    return true;
  };

  return {
    validateStep1,
    validateStep2,
    validateFinalSubmission,
  };
};
