import { useTranslation } from "react-i18next";
import Toast from "react-native-toast-message";
import { router } from "expo-router";
import { AppDispatch } from "@/store/store";
import { CreateComplaintPayload } from "@/types/complaint";
import {
  complaintInsertUpdateAction,
  getComplaintsListAction,
} from "@/store/actions/complaint";

interface SubmissionParams {
  isSubmitting: boolean;
  setIsSubmitting: (loading: boolean) => void;
  locationCoordinates: { latitude: number; longitude: number } | null;
  setLocationCoordinates: (
    coordinates: { latitude: number; longitude: number } | null
  ) => void;
  getCurrentLocation: () => Promise<{
    latitude: number;
    longitude: number;
  } | null>;
  dispatch: AppDispatch;
  selectedIssue: string;
  customDescription: string;
  watch: (field: string) => any;
  router: any;
}

export const useComplaintSubmission = ({
  isSubmitting,
  setIsSubmitting,
  locationCoordinates,
  setLocationCoordinates,
  getCurrentLocation,
  dispatch,
  selectedIssue,
  customDescription,
  watch,
  router,
}: SubmissionParams) => {
  const { t } = useTranslation();

  const getLocationCoordinates = async () => {
    if (!locationCoordinates) {
      const freshCoordinates = await getCurrentLocation();

      if (!freshCoordinates) {
        Toast.show({
          type: "error",
          text1: t("error.title"),
          text2: t("complaint.location.required"),
        });
        setIsSubmitting(false);
        return null;
      }

      setLocationCoordinates(freshCoordinates);
      return freshCoordinates;
    }
    return locationCoordinates;
  };

  const buildFinalDescription = () => {
    if (selectedIssue === "other") {
      return customDescription;
    } else {
      let finalDescription = "";
      if (watch("description")) {
        finalDescription += watch("description");
      }
      return finalDescription;
    }
  };

  const buildComplaintPayload = (
    data: any,
    coordinates: { latitude: number; longitude: number }
  ) => {
    const finalDescription = buildFinalDescription();

    const billingAddressId = data.selectedBillingAddressId;
    const shippingAddressId = data.useBillingAsShipping
      ? data.selectedBillingAddressId
      : data.selectedShippingAddressId;

    const payload: any = {
      complaint_for: (data.complaintFor === "vendor" ? 1 : 2) as 1 | 2,
      complaint_type: (data.batteryBrand === "ozone" ? 1 : 2) as 1 | 2,
      latitude: coordinates?.latitude,
      longitude: coordinates?.longitude,
      billing_address_id: billingAddressId,
      shipping_address_id: shippingAddressId,
    };

    // Add complaint issue if selected
    if (data.selectedIssue) {
      payload.complaint_issue_id = Number(data.selectedIssue);
    }

    // Add description - required when complaint_issue_id is not available
    if (!data.selectedIssue) {
      // Description is required when no issue is selected
      payload.description = finalDescription || "";
    } else if (finalDescription) {
      // Description is optional when issue is selected, but include if provided
      payload.description = finalDescription;
    }

    // Handle user information based on manual entry or existing user
    if (data.selectedVendor || data.selectedCustomer) {
      // Existing user
      payload.user_id =
        data.selectedVendor?.user_id || data.selectedCustomer?.user_id;
    } else {
      // Manual entry
      payload.first_name =
        data.complaintFor === "vendor"
          ? data.vendorFirstName
          : data.customerFirstName;
      payload.last_name =
        data.complaintFor === "vendor"
          ? data.vendorLastName
          : data.customerLastName;
      payload.country_code_alpha =
        data.complaintFor === "vendor"
          ? data.vendorCountryCode?.country_code_alpha
          : data.customerCountryCode?.country_code_alpha;
      payload.mobile =
        data.complaintFor === "vendor"
          ? data.vendorMobileNumber
          : data.customerMobileNumber;
    }

    // Handle product information based on battery brand
    if (data.batteryBrand === "ozone") {
      // Ozone battery - QR code or serial number
      if (data.qrCode) {
        payload.product_qr_code_slug = data.qrCode;
      } else if (data.serialNumber) {
        payload.serial_no = data.serialNumber;
      }
    } else {
      // 3rd party battery - required fields
      if (data.productImages && data.productImages.length > 0) {
        payload.product_serial_photo = data.productImages.map(
          (image: string) => ({ image })
        );
      }
      if (data.brandName) {
        payload.brand_name = data.brandName;
      }
    }

    // Remove keys with null or undefined values
    const filteredPayload = Object.fromEntries(
      Object.entries(payload).filter(
        ([, value]) => value !== null && value !== undefined
      )
    );

    return filteredPayload as unknown as CreateComplaintPayload;
  };

  const onSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      const coordinates = await getLocationCoordinates();
      if (!coordinates) return;

      const payload = buildComplaintPayload(data, coordinates);
      const response = await dispatch(
        complaintInsertUpdateAction(payload)
      ).unwrap();

      if (response.status) {
        Toast.show({
          type: "success",
          text1: response.message || t("complaint.submitted_successfully"),
        });
        dispatch(getComplaintsListAction({}));
        router.back();
      } else {
        Toast.show({
          type: "error",
          text1: response.message || t("complaint.submission_failed"),
        });
      }
    } catch (error: any) {
      console.error("Complaint submission error:", error);
      Toast.show({
        type: "error",
        text1: error?.message || t("complaint.submission_failed"),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    onSubmit,
  };
};
