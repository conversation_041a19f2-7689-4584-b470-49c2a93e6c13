import { styled } from "@/utils/styled";
import { View, Text, Pressable, ScrollView } from "react-native";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled(View)`
  flex: 1;
  padding: 20px;
`;

export const FormContainer = styled(View)`
  flex: 1;
`;

export const FormTitle = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 32px;
  text-align: left;
`;

export const FormSection = styled(View)`
  margin-bottom: 24px;
`;

export const FormLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 12px;
`;

export const FormField = styled(View)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border || "#E9ECEF"};
  border-radius: 8px;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const FormFieldTouchable = styled(Pressable)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border || "#E9ECEF"};
  border-radius: 8px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  min-height: 56px;
`;

export const FormFieldText = styled(Text)<{ $hasValue?: boolean }>`
  font-size: 16px;
  color: ${({ theme, $hasValue }) =>
    $hasValue ? theme.colors.text : theme.colors.gray};
  flex: 1;
`;

export const FormFieldPlaceholder = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  flex: 1;
`;

export const FormError = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.error};
  margin-top: 4px;
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  gap: 12px;
  margin-top: 24px;
`;

export const CancelButton = styled(Pressable)`
  flex: 1;
  padding: 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border || "#E9ECEF"};
  background-color: transparent;
  align-items: center;
  justify-content: center;
`;

export const SubmitButton = styled(Pressable)`
  flex: 1;
  padding: 16px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
  align-items: center;
  justify-content: center;
`;

export const ButtonText = styled(Text)<{ variant?: "cancel" | "submit" }>`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme, variant }) =>
    variant === "cancel" ? theme.colors.text : theme.colors.white};
`;

export const BottomContainer = styled(View)`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.colors.background};
  padding: 16px 20px;
  padding-bottom: ${({ theme }) => theme.safeArea?.bottom || 16}px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border || "#E9ECEF"};
  elevation: 8;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px -2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
`;

export const SubmitButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.white};
  text-align: center;
`;

export const LoadingOverlay = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.overlay};
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

// New styled components for EstimationForm
export const ScrollContainer = styled(ScrollView)`
  flex: 1;
`;

export const ScrollContentContainer = styled(View)`
  padding-bottom: 100px;
`;

export const PartChipsContainer = styled(View)`
  flex: 1;
`;

export const PartChip = styled(View)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding-vertical: 8px;
  padding-horizontal: 12px;
  margin-vertical: 4px;
`;

export const PartChipContent = styled(View)`
  flex: 1;
`;

export const PartChipText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
`;

export const PartChipRemoveButton = styled(Pressable)`
  margin-left: 8px;
`;

export const PartItemContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const PartItemContent = styled(View)`
  flex: 1;
`;

export const PartItemTitle = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const PartItemSubtitle = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-top: 4px;
`;

export const RequiredText = styled(Text)`
  color: red;
`;

export const ChevronIcon = styled(View)`
  margin-left: 8px;
`;
