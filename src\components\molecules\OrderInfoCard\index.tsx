import React from "react";
import { useTranslation } from "react-i18next";
import { OrderStatusEnum, OrderType } from "@/types/order";
import InfoRow from "@/components/atoms/InfoRow";
import {
  Container,
  HeaderContainer,
  OrderNumberContainer,
  OrderNumber,
  OrderDate,
  InfoGrid,
} from "./styles";

interface OrderInfoCardProps {
  orderNumber: string;
  orderDate: string;
  status: OrderStatusEnum;
  statusLabel: string;
  totalAmount: string;
  paymentStatus?: string;
  orderType?: string;
}

const OrderInfoCard: React.FC<OrderInfoCardProps> = ({
  orderNumber,
  orderDate,
  status,
  statusLabel,
  totalAmount,
  paymentStatus,
  orderType,
}) => {
  const { t } = useTranslation();

  return (
    <Container>
      <HeaderContainer>
        <OrderNumberContainer>
          <OrderNumber>#{orderNumber}</OrderNumber>
          <OrderDate>{orderDate}</OrderDate>
        </OrderNumberContainer>
      </HeaderContainer>

      <InfoGrid>
        <InfoRow
          label={t("order.total")}
          value={`₹${totalAmount}`}
          variant="bold"
        />
        {paymentStatus && (
          <InfoRow
            label={t("order.payment_status")}
            value={paymentStatus === "1" ? t("order.paid") : t("order.unpaid")}
            variant={paymentStatus === "1" ? "accent" : "default"}
          />
        )}
        {orderType && (
          <InfoRow
            label={t("order.type")}
            value={
              Number(orderType) === OrderType.Direct
                ? t("order.direct")
                : t("order.complient")
            }
          />
        )}
      </InfoGrid>
    </Container>
  );
};

export default OrderInfoCard;
