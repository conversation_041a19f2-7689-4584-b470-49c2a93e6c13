import { useColorScheme } from "react-native";

export interface Theme {
  colors: {
    primary: string;
    primaryLight: string;
    background: string;
    card: string;
    cardbackground: string;
    text: string;
    error: string;
    white: string;
    lightGray: string;
    border: string;
    success: string;
    warning: string;
    info: string;
    gray: string;
    darkGray: string;
    black: string;
    transparent: string;
    overlay: string;
    disabled: string;
    disable: string;
    placeholder: string;
    // Additional colors
    secondary: string;
    link: string;
    divider: string;
    divid: string;
    inputBackground: string;
    shadow: string;
    // Settings specific colors
    switchTrack: string;
    switchThumb: string;
    switchTrackActive: string;
    switchThumbActive: string;
    selectedItemBg: string;
    icon: string;
    iconSecondary: string;
    // Status colors
    statusPending: string;
    statusPendingBg: string;
    statusInReview: string;
    statusInReviewBg: string;
    statusEstimation: string;
    statusEstimationBg: string;
    statusApproved: string;
    statusApprovedBg: string;
    statusRepair: string;
    statusRepairBg: string;
    statusInProgress: string;
    statusInProgressBg: string;
    statusResolved: string;
    statusResolvedBg: string;
    statusRejected: string;
    statusRejectedBg: string;
    statusClosed: string;
    statusClosedBg: string;
    red: string;
  };
}

const lightTheme: Theme = {
  colors: {
    primary: "#007AFF",
    primaryLight: "#e3eaf6",
    background: "#FFFFFF",
    card: "#F2F2F7",
    cardbackground: "#FFFFFF",
    text: "#000000",
    error: "#FF3B30",
    white: "#FFFFFF",
    lightGray: "#F2F2F7",
    border: "#E5E5EA",
    success: "#34C759",
    warning: "#FF9500",
    info: "#5856D6",
    gray: "#8E8E93",
    darkGray: "#636366",
    black: "#000000",
    transparent: "transparent",
    overlay: "rgba(0, 0, 0, 0.5)",
    disabled: "#C7C7CC",
    disable: "#C7C7CC",
    placeholder: "#C7C7CC",
    // Additional colors
    secondary: "#4D7CFE",
    link: "#4D7CFE",
    divider: "#E0E0E0",
    divid: "#e5e5e5",
    inputBackground: "#F3F6FA",
    shadow: "#000000",
    // Settings specific colors
    switchTrack: "#767577",
    switchThumb: "#f4f3f4",
    switchTrackActive: "#81b0ff",
    switchThumbActive: "#007AFF",
    selectedItemBg: "#F0F8FF",
    icon: "#333333",
    iconSecondary: "#999999",
    // Status colors
    statusPending: "#F57C00",
    statusPendingBg: "#FFF3E0",
    statusInReview: "#3F51B5",
    statusInReviewBg: "#E8EAF6",
    statusEstimation: "#0097A7",
    statusEstimationBg: "#E0F7FA",
    statusApproved: "#388E3C",
    statusApprovedBg: "#E8F5E9",
    statusRepair: "#7B1FA2",
    statusRepairBg: "#F3E5F5",
    statusInProgress: "#1976D2",
    statusInProgressBg: "#E3F2FD",
    statusResolved: "#388E3C",
    statusResolvedBg: "#E8F5E9",
    statusRejected: "#D32F2F",
    statusRejectedBg: "#FFEBEE",
    statusClosed: "#546E7A",
    statusClosedBg: "#ECEFF1",
    red: "#FF3B30",
  },
};

const darkTheme: Theme = {
  colors: {
    primary: "#0A84FF",
    primaryLight: "#1a1a2e",
    background: "#000000",
    card: "#3A3A3C",
    cardbackground: "#3A3A3C",
    text: "#FFFFFF",
    error: "#FF453A",
    white: "#FFFFFF",
    lightGray: "#1C1C1E",
    border: "#38383A",
    success: "#30D158",
    warning: "#FF9F0A",
    info: "#5E5CE6",
    gray: "#8E8E93",
    darkGray: "#636366",
    black: "#000000",
    transparent: "transparent",
    overlay: "rgba(0, 0, 0, 0.7)",
    disabled: "#3A3A3C",
    disable: "#C7C7CC",
    placeholder: "#3A3A3C",
    // Additional colors
    secondary: "#0A84FF",
    link: "#5E97F6",
    divider: "#8E8E93",
    divid: "#e5e5e5",
    inputBackground: "#1C1C1E",
    shadow: "#000000",
    // Settings specific colors
    switchTrack: "#555555",
    switchThumb: "#b0b0b0",
    switchTrackActive: "#34C759",
    switchThumbActive: "#ffffff",
    selectedItemBg: "#1C1C1E",
    icon: "#FFFFFF",
    iconSecondary: "#8E8E93",
    // Status colors
    statusPending: "#FFB74D",
    statusPendingBg: "#FFF3E0",
    statusInReview: "#7986CB",
    statusInReviewBg: "#E8EAF6",
    statusEstimation: "#4DD0E1",
    statusEstimationBg: "#E0F7FA",
    statusApproved: "#81C784",
    statusApprovedBg: "#E8F5E9",
    statusRepair: "#BA68C8",
    statusRepairBg: "#F3E5F5",
    statusInProgress: "#64B5F6",
    statusInProgressBg: "#E3F2FD",
    statusResolved: "#81C784",
    statusResolvedBg: "#E8F5E9",
    statusRejected: "#E57373",
    statusRejectedBg: "#FFEBEE",
    statusClosed: "#90A4AE",
    statusClosedBg: "#ECEFF1",
    red: "#FF3B30",
  },
};

export const useTheme = () => {
  const colorScheme = useColorScheme();
  const theme = colorScheme === "dark" ? darkTheme : lightTheme;
  const isDark = colorScheme === "dark";
  return { theme, isDark };
};
