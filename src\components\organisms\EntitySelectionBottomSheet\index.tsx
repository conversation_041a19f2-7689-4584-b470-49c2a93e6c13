import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetFlatList, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { VendorDetails } from "@/types/vendor";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  Title,
  SearchContainer,
  SearchInput,
  EntityItem,
  EntityInfo,
  EntityName,
  EntityId,
  EmptyContainer,
  EmptyText,
  AddManualButton,
  AddManualText,
  ItemsCountText,
  StyledBottomSheetView,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { Customer } from "@/types/customer";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { getCustomerListAction } from "@/store/actions/customer";
import { getVendorListAction } from "@/store/actions/vendor";
import { useDebounce } from "@/utils/useDebounce";
import { GetCustomerListParams } from "@/types/customer";
import { VendorListPayloadType } from "@/types/vendor";
import { View } from "react-native";

interface EntitySelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (entity: VendorDetails | Customer) => void;
  onAddManual: () => void;
  entityType: "vendor" | "customer";
  entities?: (VendorDetails | Customer)[];
  isLoading?: boolean;
  selectedEntity?: VendorDetails | Customer;
}

const EntitySelectionBottomSheet: React.FC<EntitySelectionBottomSheetProps> = ({
  isVisible,
  onClose,
  onSelect,
  onAddManual,
  entityType,
  entities: propEntities,
  isLoading: propIsLoading = false,
  selectedEntity,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const {
    customers: reduxCustomers,
    customersLoading: isCustomerListLoading,
    isLoadingMore: isCustomerLoadingMore,
    current_page: customerCurrentPage,
    last_page: customerLastPage,
  } = useAppSelector((state: RootState) => state.customer);

  const {
    vendorList: reduxVendorList,
    isVendorListLoading,
    isLoadingMore: isVendorLoadingMore,
    current_page: vendorCurrentPage,
    last_page: vendorLastPage,
  } = useAppSelector((state: RootState) => state.vendor);

  const debouncedSearch = useDebounce(query, 400);

  const entities = Array.isArray(propEntities)
    ? propEntities
    : entityType === "customer"
    ? (reduxCustomers as (VendorDetails | Customer)[])
    : (reduxVendorList as (VendorDetails | Customer)[]);

  const isLoading =
    propIsLoading ||
    (entityType === "customer" ? isCustomerListLoading : isVendorListLoading);

  const isLoadingMore =
    entityType === "customer" ? isCustomerLoadingMore : isVendorLoadingMore;

  const currentPage =
    entityType === "customer" ? customerCurrentPage : vendorCurrentPage;

  const lastPage =
    entityType === "customer" ? customerLastPage : vendorLastPage;

  const fetchEntities = useCallback(
    async (params: GetCustomerListParams | VendorListPayloadType) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        if (entityType === "customer") {
          await dispatch(
            getCustomerListAction(params as GetCustomerListParams)
          ).unwrap();
        } else {
          await dispatch(
            getVendorListAction(params as VendorListPayloadType)
          ).unwrap();
        }
      } catch (error: any) {
        console.error("fetchEntities error:", error);
      } finally {
        setIsRefreshing(false);
      }
    },
    [dispatch, entityType]
  );

  useEffect(() => {
    if (isVisible && entities.length === 0) {
      fetchEntities({ page: 1 });
    }
  }, []);

  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length >= 3) {
      fetchEntities({
        search: debouncedSearch,
      });
    } else if (debouncedSearch === "") {
      fetchEntities({ page: 1 });
    }
  }, [debouncedSearch, fetchEntities]);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["80%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && currentPage < lastPage) {
      const payload: any = {
        page: currentPage + 1,
      };
      if (debouncedSearch) payload.search = debouncedSearch;
      fetchEntities(payload);
    }
  }, [isLoadingMore, currentPage, lastPage, debouncedSearch, fetchEntities]);

  const handleEntitySelect = useCallback(
    (entity: VendorDetails | Customer) => {
      onSelect(entity);
      onClose();
    },
    [onSelect, onClose]
  );

  const handleAddManual = useCallback(() => {
    onAddManual();
    onClose();
  }, [onAddManual, onClose]);

  const renderEntityItem = useCallback(
    ({ item }: { item: VendorDetails | Customer }) => {
      let displayName: string;
      let displayId: string;
      let isSelected = false;

      if (entityType === "vendor") {
        const vendor = item as VendorDetails;
        displayName = `${vendor.first_name} ${vendor.last_name}`;
        displayId = vendor.email || vendor.mobile?.toString() || "";
        isSelected = selectedEntity && (selectedEntity as VendorDetails).user_id === vendor.user_id;
      } else {
        const customer = item as Customer;
        displayName = customer.name;
        displayId = customer.email || customer.mobile?.toString() || "";
        isSelected = selectedEntity && (selectedEntity as Customer).user_id === customer.user_id;
      }

      return (
        <EntityItem onPress={() => handleEntitySelect(item)} isSelected={isSelected}>
          <EntityInfo>
            <EntityName>{displayName}</EntityName>
            {displayId && <EntityId>{displayId}</EntityId>}
          </EntityInfo>
        </EntityItem>
      );
    },
    [handleEntitySelect, entityType, selectedEntity]
  );

  const renderEmptyComponent = useCallback(() => {
    if (isLoading && !isRefreshing) {
      return <LoadingOverlay isLoading={true} size="large" />;
    }

    return (
      <EmptyContainer>
        <EmptyText>
          {entityType === "vendor"
            ? t("common.no_vendors_available")
            : t("common.no_customers_available")}
        </EmptyText>
        <AddManualButton onPress={handleAddManual}>
          <Ionicons
            name="add-circle-outline"
            size={20}
            color={theme.colors.white}
          />
          <AddManualText>{t("complaint.add_manually")}</AddManualText>
        </AddManualButton>
      </EmptyContainer>
    );
  }, [
    isLoading,
    isRefreshing,
    entityType,
    handleAddManual,
    t,
    theme.colors.white,
  ]);

  const renderFooter = useCallback(() => {
    if (isLoadingMore) {
      return (
        <View style={{ paddingVertical: 30 }}>
          <LoadingOverlay isLoading={true} size="small" />
        </View>
      );
    }
    return null;
  }, [isLoadingMore]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      backdropComponent={renderBackdrop}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enableDynamicSizing={false}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <Title>
          {entityType === "vendor"
            ? t("complaint.form.select_vendor")
            : t("complaint.form.select_customer")}
        </Title>

        <SearchContainer>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={
              entityType === "vendor"
                ? t("cart.searchVendor")
                : t("common.search_customers")
            }
            value={query}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.text}
          />
        </SearchContainer>

        <ItemsCountText>
          {entities.length}{" "}
          {entityType === "vendor"
            ? t("vendorManagement.vendors")
            : t("common.customers")}
        </ItemsCountText>

        <BottomSheetFlatList
          data={entities || []}
          keyExtractor={(item) => item.user_id?.toString() || `${entityType}-${Math.random()}`}
          renderItem={renderEntityItem}
          ListEmptyComponent={renderEmptyComponent}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          showsVerticalScrollIndicator={false}
          refreshing={isRefreshing}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default EntitySelectionBottomSheet;
