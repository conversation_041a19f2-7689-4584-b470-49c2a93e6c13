export interface ApiResponse {
  status: boolean;
  message: string;
  data?: any;
  error?: string;
  pagination?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export enum UserType {
  VENDOR = 2,
  SALESPERSON = 3,
  CUSTOMER = 5,
  SERVICEPERSON = 7,
}

export enum AddVendorStep {
  BASICINFO = 1,
  ADDRESS = 2,
}

export type SortType =
  | "Title: A-Z"
  | "Title: Z-A"
  | "Price: High to Low"
  | "Price: Low to High";

export interface ImageConfig {
  fieldName: string;
  images: Array<{ image: string }>;
  fileNamePrefix?: string;
}
