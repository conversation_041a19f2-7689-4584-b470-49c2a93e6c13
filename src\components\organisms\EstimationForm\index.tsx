import React, { useState, use<PERSON><PERSON>back, useEffect, useRef } from "react";
import { useFormContext } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { Part } from "@/types/complaint";
import QuantitySelector from "@/components/molecules/QuantitySelector";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getPartListAction } from "@/store/actions/complaint";
import { useDebounce } from "@/utils/useDebounce";
import Toast from "react-native-toast-message";
import {
  FormContainer,
  FormTitle,
  FormSection,
  FormLabel,
  FormFieldTouchable,
  FormFieldText,
  FormFieldPlaceholder,
  FormError,
  ScrollContainer,
  PartChipsContainer,
  PartChip,
  PartChipContent,
  PartChipText,
  PartChipRemoveButton,
  PartItemContainer,
  <PERSON><PERSON>tem<PERSON>ontent,
  Part<PERSON>temT<PERSON>le,
  Required<PERSON><PERSON><PERSON>,
  BottomContainer,
  SubmitButton,
  SubmitButtonText,
} from "@/styles/Estimation.styles";
import PickerBottomSheet, {
  PickerOption,
} from "@/components/molecules/PickerField/PickerBottomSheet";

interface EstimationFormData {
  estimate_time: string;
  products: Array<{
    part: Part;
    slug: string;
    quantity: number;
  }>;
}

interface EstimationFormProps {
  loading: boolean;
  onSubmit: (data: EstimationFormData) => void;
  t?: any;
}

const EstimationForm: React.FC<EstimationFormProps> = ({
  loading,
  onSubmit,
  t,
}) => {
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<EstimationFormData>();

  const [showPartSelector, setShowPartSelector] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [estimateTime, setEstimateTime] = useState<Date | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const lastSearchRef = useRef<string>("");
  const retryCountRef = useRef<number>(0);

  const selectedParts = watch("products") || [];
  const { partList, partListLoading, partListCurrentPage, partListLastPage } =
    useAppSelector((state) => state.complaints);
  const debouncedSearch = useDebounce(searchQuery, 800);

  const fetchParts = useCallback(
    async (params: { page: number; search?: string }) => {
      try {
        const searchKey = params.search || "";
        if (lastSearchRef.current === searchKey && params.page === 1) return;

        lastSearchRef.current = searchKey;
        setIsSearching(true);
        await dispatch(getPartListAction(params)).unwrap();
        retryCountRef.current = 0;
      } catch (error: any) {
        console.error("fetchParts error:", error);
        if (
          error?.message?.includes("Too Many Attempts") ||
          error?.message?.includes("ThrottleRequestsException")
        ) {
          retryCountRef.current += 1;
          const delay = Math.min(
            1000 * Math.pow(2, retryCountRef.current),
            10000
          );
          Toast.show({
            type: "error",
            text1: t("common.rate_limit_error") || "Rate limit exceeded",
            text2:
              t("common.please_wait") || "Please wait a moment and try again",
          });
          setTimeout(() => {
            if (retryCountRef.current < 3) fetchParts(params);
          }, delay);
        } else {
          Toast.show({
            type: "error",
            text1: t("common.error") || "Error",
            text2:
              error?.message ||
              t("common.failed_to_load_parts") ||
              "Failed to load products",
          });
        }
      } finally {
        setIsSearching(false);
      }
    },
    [dispatch, t]
  );

  useEffect(() => {
    if (showPartSelector && partList.length === 0) fetchParts({ page: 1 });
  }, [showPartSelector, partList.length, fetchParts]);

  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length >= 3) {
      fetchParts({ page: 1, search: debouncedSearch });
    } else if (debouncedSearch === "" && showPartSelector) {
      fetchParts({ page: 1 });
    }
  }, [debouncedSearch, fetchParts, showPartSelector]);

  const handlePartSelect = useCallback(
    async (part: PickerOption) => {
      const selectedPart = part as unknown as Part;
      const newPart = {
        part: selectedPart,
        slug: selectedPart.slug,
        quantity: 1,
      };
      const existingIndex = selectedParts.findIndex(
        (p) => p.slug === newPart.slug
      );

      if (existingIndex === -1) {
        setValue("products", [...selectedParts, newPart]);
      } else {
        const updatedParts = [...selectedParts];
        updatedParts[existingIndex].quantity = Math.min(
          999,
          updatedParts[existingIndex].quantity + 1
        );
        setValue("products", updatedParts);
      }
      setShowPartSelector(false);
    },
    [setValue, selectedParts]
  );

  const handleRemovePart = (slug: string) => {
    setValue(
      "products",
      selectedParts.filter((part) => part.slug !== slug)
    );
  };

  const handleQuantityChange = (index: number, newQuantity: number) => {
    const updatedParts = [...selectedParts];
    updatedParts[index].quantity = Math.max(1, Math.min(999, newQuantity));
    setValue("products", updatedParts);
  };

  const handleDateChange = (selectedDate: Date) => {
    setShowDatePicker(false);
    setEstimateTime(selectedDate);
    // Format as 'YYYY-MM-DD HH:mm:ss'
    const pad = (n: number) => n.toString().padStart(2, "0");
    const formatted = `${selectedDate.getFullYear()}-${pad(
      selectedDate.getMonth() + 1
    )}-${pad(selectedDate.getDate())} ${pad(selectedDate.getHours())}:${pad(
      selectedDate.getMinutes()
    )}:${pad(selectedDate.getSeconds())}`;
    setValue("estimate_time", formatted);
  };

  const formatEstimateTime = (date: Date) => {
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const handleLoadMore = useCallback(() => {
    if (partListCurrentPage < partListLastPage) {
      const payload: any = { page: partListCurrentPage + 1 };
      if (debouncedSearch) payload.search = debouncedSearch;
      fetchParts(payload);
    }
  }, [partListCurrentPage, partListLastPage, debouncedSearch, fetchParts]);

  const renderPartItem = useCallback((part: PickerOption) => {
    const item = part as unknown as Part;
    return (
      <PartItemContainer>
        <PartItemContent>
          <PartItemTitle>{item.name}</PartItemTitle>
        </PartItemContent>
      </PartItemContainer>
    );
  }, []);

  const partListAsPickerOptions = partList.map((part) => ({
    ...part,
    id: part.part_id,
  })) as PickerOption[];

  return (
    <>
      <ScrollContainer
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        <FormContainer>
          <FormTitle>
            {t("estimation.send_estimation_details") ||
              "Send Estimation Details"}
          </FormTitle>

          {/* Estimate Time */}
          <FormSection>
            <FormLabel>
              {t("estimation.estimate_time") || "Estimate Time"}{" "}
              <RequiredText>*</RequiredText>
            </FormLabel>
            <FormFieldTouchable
              onPress={() => setShowDatePicker(true)}
              disabled={loading}
            >
              <FormFieldText $hasValue={!!estimateTime}>
                {estimateTime
                  ? formatEstimateTime(estimateTime)
                  : t("estimation.enter_estimate_time") ||
                    "Enter estimate time"}
              </FormFieldText>
            </FormFieldTouchable>
            {errors.estimate_time && (
              <FormError>{errors.estimate_time.message}</FormError>
            )}
          </FormSection>

          {/* Part Selection */}
          <FormSection>
            <FormLabel>
              {t("estimation.select_part") || "Select Parts"}
            </FormLabel>
            <FormFieldTouchable
              onPress={() => setShowPartSelector(true)}
              disabled={loading}
            >
              <FormFieldPlaceholder>
                {t("estimation.select_part") || "Select part"}
              </FormFieldPlaceholder>
              <Ionicons
                name="chevron-down"
                size={20}
                color={theme.colors.gray}
              />
            </FormFieldTouchable>
            {errors.products && errors.products.length > 0 && (
              <FormError>
                {errors.products[0]?.message || "Parts selection error"}
              </FormError>
            )}

            <PartChipsContainer>
              {selectedParts.map((item, index) => (
                <PartChip key={index}>
                  <PartChipContent>
                    <PartChipText>{item.part.name}</PartChipText>
                  </PartChipContent>
                  <QuantitySelector
                    quantity={item.quantity}
                    onIncrement={() =>
                      handleQuantityChange(index, item.quantity + 1)
                    }
                    onDecrement={() =>
                      handleQuantityChange(index, item.quantity - 1)
                    }
                    minQuantity={1}
                    maxQuantity={999}
                    disabled={loading}
                  />
                  <PartChipRemoveButton
                    onPress={() => handleRemovePart(item.slug)}
                  >
                    <Ionicons
                      name="close"
                      size={20}
                      color={theme.colors.gray}
                    />
                  </PartChipRemoveButton>
                </PartChip>
              ))}
            </PartChipsContainer>
          </FormSection>

          <DateTimePickerModal
            isVisible={showDatePicker}
            mode="datetime"
            onConfirm={handleDateChange}
            onCancel={() => setShowDatePicker(false)}
            date={estimateTime || new Date()}
            minimumDate={new Date()}
          />
        </FormContainer>

        <PickerBottomSheet
          isVisible={showPartSelector}
          onClose={() => setShowPartSelector(false)}
          onSelect={handlePartSelect}
          data={partListAsPickerOptions}
          title={t("estimation.select_part")}
          searchPlaceholder={t("estimation.search_parts")}
          displayKey="name"
          isLoading={partListLoading || isSearching}
          searchable={true}
          renderItem={renderPartItem}
          hasPagination={true}
          currentPage={partListCurrentPage}
          lastPage={partListLastPage}
          onLoadMore={handleLoadMore}
          isLoadingMore={false}
          itemsCountText={`${partList.length} ${t("common.parts")}`}
          onSearch={setSearchQuery}
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          isMultiple={true}
          selectedItems={selectedParts.map((p) => p.slug)}
        />
      </ScrollContainer>
      <BottomContainer>
        <SubmitButton
          onPress={onSubmit}
          disabled={loading}
          style={{ opacity: loading ? 0.6 : 1 }}
        >
          <SubmitButtonText>
            {loading
              ? t("estimation.submitting") || "Submitting..."
              : t("estimation.send_estimation") || "Send Estimation"}
          </SubmitButtonText>
        </SubmitButton>
      </BottomContainer>
    </>
  );
};

export default EstimationForm;
