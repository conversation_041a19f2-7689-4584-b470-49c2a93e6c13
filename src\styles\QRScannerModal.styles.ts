import { styled } from "../utils/styled";
import { Dimensions } from "react-native";

const { width } = Dimensions.get("window");
const SCAN_SIZE = width * 0.7;

export const Container = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background  };
  justify-content: center;
  align-items: center;
`;

export const Centered = styled.View`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.overlay};
  justify-content: center;
  align-items: center;
`;

export const ScannerContainer = styled.View`
  width: ${SCAN_SIZE}px;
  height: ${SCAN_SIZE}px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin-top: 60px;
  margin-bottom: 32px;
  border-radius: 16px;
`;

export const ScanFrame = styled.View`
  position: absolute;
  width: 100%;
  height: 100%;
  border-color: ${({ theme }) => theme.colors.primary};
  border-width: 4px;
  border-radius: 16px;
  border-style: solid;
`;

export const Actions = styled.View`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
`;

export const ActionBtn = styled.Pressable`
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: ${({ theme }) => theme.colors.white};
  justify-content: center;
  align-items: center;
  margin-horizontal: 16px;
  elevation: 2;
`;

export const CloseBtn = styled.Pressable`
  position: absolute;
  top: 48px;
  right: 32px;
  z-index: 10;
  background-color: ${({ theme }) => theme.colors.overlay};
  border-radius: 20px;
  padding: 4px;
`;
