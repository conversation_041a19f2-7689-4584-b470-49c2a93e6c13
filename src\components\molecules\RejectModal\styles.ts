import { styled } from "@/utils/styled";
import { View, Text, Pressable, TextInput } from "react-native";

interface ButtonTextProps {
  variant: "cancel" | "confirm";
}

interface FormTextAreaProps {
  hasError?: boolean;
}

export const ModalOverlay = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.overlay};
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const ModalContent = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 3.84px;
  elevation: 5;
  overflow: hidden;
`;

export const ModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px 20px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider};
`;

export const ModalTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
`;

export const CloseButton = styled(Pressable)`
  padding: 4px;
`;

export const FormContainer = styled(View)`
  padding: 20px;
`;

export const FormField = styled(View)`
  margin-bottom: 16px;
`;

export const FormLabel = styled(Text)`
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
`;

export const FormTextArea = styled(TextInput)<FormTextAreaProps>`
  border-width: 1px;
  border-color: ${({ theme, hasError }) => 
    hasError ? theme.colors.error : theme.colors.border};
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  background-color: ${({ theme }) => theme.colors.background};
  min-height: 100px;
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  gap: 12px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  background-color: ${({ theme }) => theme.colors.card};
`;

export const CancelButton = styled(Pressable)`
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.background};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
`;

export const ConfirmButton = styled(Pressable)`
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.error || "#DC3545"};
`;

export const ButtonText = styled(Text)<ButtonTextProps>`
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme, variant }) =>
    variant === "cancel" ? theme.colors.text : theme.colors.white};
`;
