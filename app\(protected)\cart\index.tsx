import React, { useState, useCallback, useEffect, useMemo } from "react";
import { View } from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import { useTheme } from "@/hooks/useTheme";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { useAppSelector, useAppDispatch } from "@/store/store";
import { checkCouponCodeAction } from "@/store/actions/order";
import Header from "@/components/organisms/Header";
import Stepper from "@/components/molecules/Stepper";
import {
  CartStep,
  VendorStep,
  AddressStep,
  PaymentStep,
  SummaryStep,
} from "@/components/cart";
import { CartButton } from "@/components/atoms";
import {
  Container,
  StepperContainer,
  ContentContainer,
  EmptyContainer,
  EmptyText,
} from "@/styles/Cart.styles";
import { CheckoutStep } from "@/types/cart";
import { UserType } from "@/types/api";
import { VendorDetails } from "@/types/vendor";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";
import { useCart } from "@/hooks/useCart";
import { useOrderManagement } from "@/hooks/useOrderManagement";

const CartScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();

  const { cartList, fetchCartList } = useCart();
  const { handlePlaceOrder, isPlacingOrder } = useOrderManagement();
  const user = useAppSelector((state) => state.auth.user);

  const [currentStep, setCurrentStep] = useState<CheckoutStep>(
    CheckoutStep.CART
  );

  const [selectedBillingAddressId, setSelectedBillingAddressId] = useState<
    number | null
  >(null);
  const [selectedShippingAddressId, setSelectedShippingAddressId] = useState<
    number | null
  >(null);
  const [useBillingAsShipping, setUseBillingAsShipping] =
    useState<boolean>(true);
  const [selectedVendor, setSelectedVendor] = useState<VendorDetails | null>(
    null
  );
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<PaymentMethod | null>(null);
  const [paymentProof, setPaymentProof] =
    useState<ImagePicker.ImagePickerAsset | null>(null);
  const [selectedPaymentDetails, setSelectedPaymentDetails] = useState<{
    account_number?: string | null;
    upi_id?: string | null;
    ifsc_code?: string | null;
    qr_code?: string | null;
  } | null>(null);
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);

  // Coupon state and logic
  const [couponCode, setCouponCode] = useState<string>("");
  const [offerCartList, setOfferCartList] = useState<any>(null);
  const [isCheckingCoupon, setIsCheckingCoupon] = useState<boolean>(false);
  const [couponError, setCouponError] = useState<string | null>(null);

  const clearCouponState = useCallback(() => {
    console.log("Clearing coupon state...");
    setOfferCartList(null);
    setAppliedCoupon(null);
    setCouponError(null);
    setIsCheckingCoupon(false);
  }, []);

  const handleSetCouponCode = useCallback((code: string) => {
    console.log("Setting coupon code:", code);
    setCouponCode(code);
    if (!code.trim()) {
      console.log("Code is empty, clearing state...");
      clearCouponState();
    }
  }, [clearCouponState]);

  const handleCheckCoupon = useCallback(async () => {
    if (!couponCode.trim()) {
      setCouponError("Please enter a coupon code");
      return;
    }
    try {
      setIsCheckingCoupon(true);
      setCouponError(null);
      const response = await dispatch(
        checkCouponCodeAction({ coupon_code: couponCode.trim() })
      ).unwrap();
      setOfferCartList(response.data);
      setAppliedCoupon(response.data);
    } catch (error: any) {
      setCouponError(error.message || "Invalid coupon code");
      setOfferCartList(null);
      setAppliedCoupon(null);
      console.error("Coupon validation failed:", error);
    } finally {
      setIsCheckingCoupon(false);
    }
  }, [couponCode, dispatch]);

  // Initialize coupon state from appliedCoupon if it exists
  useEffect(() => {
    if (appliedCoupon && !offerCartList && couponCode.trim()) {
      setOfferCartList(appliedCoupon);
    }
  }, [appliedCoupon, offerCartList, couponCode]);

  const hasValidCoupon =
    offerCartList &&
    offerCartList.discount_amount &&
    Number(offerCartList?.discount_amount || 0) > 0;

  const isSalesperson = user?.role_id === UserType.SALESPERSON;

  const stepData = useMemo(() => {
    const steps = [{ id: CheckoutStep.CART, label: t("cart.steps.cart") }];

    if (isSalesperson) {
      steps.push({ id: CheckoutStep.VENDOR_SELECTION, label: "Vendor" });
    }

    steps.push(
      { id: CheckoutStep.ADDRESS_PREVIEW, label: "Address" },
      { id: CheckoutStep.SUMMARY, label: "Summary" },
      { id: CheckoutStep.PAYMENT, label: "Payment" }
    );

    return steps;
  }, [isSalesperson]);

  // Helper function to get next step
  const getNextStep = useCallback(
    (currentStep: CheckoutStep): CheckoutStep => {
      if (currentStep === CheckoutStep.CART) {
        return isSalesperson
          ? CheckoutStep.VENDOR_SELECTION
          : CheckoutStep.ADDRESS_PREVIEW;
      }
      if (currentStep === CheckoutStep.VENDOR_SELECTION) {
        return CheckoutStep.ADDRESS_PREVIEW;
      }
      if (currentStep === CheckoutStep.ADDRESS_PREVIEW) {
        return CheckoutStep.SUMMARY;
      }
      if (currentStep === CheckoutStep.SUMMARY) {
        return CheckoutStep.PAYMENT;
      }
      return currentStep;
    },
    [isSalesperson]
  );

  // Helper function to get previous step
  const getPreviousStep = useCallback(
    (currentStep: CheckoutStep): CheckoutStep => {
      if (currentStep === CheckoutStep.PAYMENT) {
        return CheckoutStep.SUMMARY;
      }
      if (currentStep === CheckoutStep.SUMMARY) {
        return CheckoutStep.ADDRESS_PREVIEW;
      }
      if (currentStep === CheckoutStep.ADDRESS_PREVIEW) {
        return isSalesperson
          ? CheckoutStep.VENDOR_SELECTION
          : CheckoutStep.CART;
      }
      if (currentStep === CheckoutStep.VENDOR_SELECTION) {
        return CheckoutStep.CART;
      }
      return currentStep;
    },
    [isSalesperson]
  );

  useEffect(() => {
    if (user?.address && Array.isArray(user.address)) {
      const billingAddresses = user.address.filter(
        (addr: any) => addr.billing_shipping === "1"
      );
      if (billingAddresses.length > 0 && !selectedBillingAddressId) {
        setSelectedBillingAddressId(billingAddresses[0].id);
      }

      const shippingAddresses = user.address.filter(
        (addr: any) => addr.billing_shipping === "2"
      );
      if (shippingAddresses.length > 0 && !selectedShippingAddressId) {
        setSelectedShippingAddressId(shippingAddresses[0].id);
      }
    }
  }, [user, selectedBillingAddressId, selectedShippingAddressId]);

  useFocusEffect(
    useCallback(() => {
      fetchCartList();
    }, [fetchCartList])
  );

  const handleNextStep = useCallback(() => {
    const nextStep = getNextStep(currentStep);
    if (nextStep !== currentStep) {
      setCurrentStep(nextStep);
    }
  }, [currentStep, getNextStep]);

  const handlePreviousStep = useCallback(() => {
    const prevStep = getPreviousStep(currentStep);
    if (prevStep !== currentStep) {
      setCurrentStep(prevStep);
    }
  }, [currentStep, getPreviousStep]);

  const handleStepSelect = useCallback(
    (stepId: number) => {
      if (stepId <= currentStep) {
        setCurrentStep(stepId as CheckoutStep);
      }
    },
    [currentStep]
  );

  const handleVendorContinue = useCallback(
    (vendor: VendorDetails) => {
      setSelectedVendor(vendor);
      handleNextStep();
    },
    [handleNextStep]
  );

  const handleCouponApplied = useCallback((couponData: any) => {
    setAppliedCoupon(couponData);
  }, []);

  const handlePaymentContinue = useCallback(
    (
      paymentMethod: PaymentMethod,
      proof?: ImagePicker.ImagePickerAsset,
      paymentDetails?: {
        account_number?: string | null;
        upi_id?: string | null;
        ifsc_code?: string | null;
        qr_code?: string | null;
      }
    ) => {
      setSelectedPaymentMethod(paymentMethod);
      setPaymentProof(proof || null);
      setSelectedPaymentDetails(paymentDetails || null);

      const effectiveShippingAddressId = useBillingAsShipping
        ? selectedBillingAddressId
        : selectedShippingAddressId;

      // Now place the order after payment is selected
      handlePlaceOrder(
        selectedVendor?.user_id,
        selectedBillingAddressId,
        effectiveShippingAddressId,
        paymentMethod,
        proof || null,
        paymentDetails || null,
        () => true // isReadyForCheckout function
      );
    },
    [
      handlePlaceOrder,
      selectedVendor,
      selectedBillingAddressId,
      selectedShippingAddressId,
      useBillingAsShipping,
    ]
  );

  const renderStepContent = useCallback(() => {
    switch (currentStep) {
      case CheckoutStep.CART:
        return <CartStep onContinue={handleNextStep} />;

      case CheckoutStep.VENDOR_SELECTION:
        return (
          <VendorStep
            onBack={handlePreviousStep}
            onContinue={handleVendorContinue}
            selectedVendor={selectedVendor}
          />
        );

      case CheckoutStep.ADDRESS_PREVIEW:
        return (
          <AddressStep
            onBack={handlePreviousStep}
            onContinue={handleNextStep}
            selectedBillingAddressId={selectedBillingAddressId}
            selectedShippingAddressId={selectedShippingAddressId}
            useBillingAsShipping={useBillingAsShipping}
            setSelectedBillingAddressId={setSelectedBillingAddressId}
            setSelectedShippingAddressId={setSelectedShippingAddressId}
            setUseBillingAsShipping={setUseBillingAsShipping}
            selectedVendor={selectedVendor}
          />
        );

      case CheckoutStep.SUMMARY:
        return (
          <SummaryStep
            onBack={handlePreviousStep}
            onContinue={handleNextStep}
            selectedBillingAddressId={selectedBillingAddressId}
            selectedShippingAddressId={selectedShippingAddressId}
            useBillingAsShipping={useBillingAsShipping}
            selectedVendor={selectedVendor}
            couponCode={couponCode}
            setCouponCode={handleSetCouponCode}
            offerCartList={offerCartList}
            setOfferCartList={setOfferCartList}
            isCheckingCoupon={isCheckingCoupon}
            setIsCheckingCoupon={setIsCheckingCoupon}
            couponError={couponError}
            setCouponError={setCouponError}
            handleCheckCoupon={handleCheckCoupon}
            hasValidCoupon={hasValidCoupon}
          />
        );

      case CheckoutStep.PAYMENT:
        return (
          <PaymentStep
            onBack={handlePreviousStep}
            onContinue={handlePaymentContinue}
            selectedPaymentMethod={selectedPaymentMethod}
            paymentProof={paymentProof}
            selectedPaymentDetails={selectedPaymentDetails}
            isPlacingOrder={isPlacingOrder}
          />
        );

      default:
        return null;
    }
  }, [
    currentStep,
    handleNextStep,
    handlePreviousStep,
    handleVendorContinue,
    handlePaymentContinue,
    selectedVendor,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    selectedPaymentMethod,
    setSelectedBillingAddressId,
    setSelectedShippingAddressId,
    setUseBillingAsShipping,
    paymentProof,
    appliedCoupon,
    couponCode,
    offerCartList,
    isCheckingCoupon,
    couponError,
    handleCheckCoupon,
    handleSetCouponCode,
    hasValidCoupon,
    isPlacingOrder,
  ]);

  if (!cartList?.cartItems?.length) {
    return (
      <Container>
        <Header
          title={t("cart.title")}
          showCart={false}
          showBack
          onBackPress={() => router.back()}
        />
        <EmptyContainer style={{ backgroundColor: theme.colors.background }}>
          <Ionicons name="cart-outline" size={64} color={theme.colors.text} />
          <EmptyText style={{ color: theme.colors.text }}>
            {t("cart.empty")}
          </EmptyText>
          <CartButton
            title={t("cart.start_shopping")}
            onPress={() => router.push("/(protected)/(tabs)/products")}
            variant="primary"
            icon="storefront"
          />
        </EmptyContainer>
      </Container>
    );
  }

  return (
    <Container>
      <Header
        title={t("cart.title")}
        showCart={false}
        showBack
        onBackPress={() => router.back()}
      />
      <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
        <StepperContainer>
          <Stepper
            stepData={stepData}
            currentId={currentStep}
            setSelectedTabNav={handleStepSelect}
          />
        </StepperContainer>
        <ContentContainer>{renderStepContent()}</ContentContainer>
      </View>
    </Container>
  );
};

export default CartScreen;
