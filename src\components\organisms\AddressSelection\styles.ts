import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const Container = styled(View)`
  flex: 1;
`;

export const Header = styled(View)`
  margin-bottom: 24px;
`;

export const Title = styled(Text)`
  font-size: 24px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
`;

export const Subtitle = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  line-height: 22px;
`;

export const SectionHeader = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: 24px 0px;
  width: 100%;
`;

export const SectionIcon = styled(View)`
  background-color: ${({ theme }) => theme.colors.primary + "20"};
  border-radius: 8px;
  padding: 8px;
  margin-right: 12px;
`;

export const SectionTitle = styled(Text)`
  font-size: 18px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
`;

export const EmptyState = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  padding: 32px;
  border-radius: 16px;
  align-items: center;
  margin: 24px 0;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-style: dashed;
`;

export const EmptyIcon = styled(View)`
  background-color: ${({ theme }) => theme.colors.primary + "20"};
  border-radius: 32px;
  padding: 16px;
  margin-bottom: 16px;
`;

export const EmptyTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-bottom: 8px;
`;

export const EmptyDescription = styled(Text)`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  line-height: 22px;
`;

export const CheckboxContainer = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  padding: 16px 20px;
  border-radius: 12px;
  margin: 16px 0;
  border: 1px solid ${({ theme }) => theme.colors.border};
  shadow-color: ${({ theme }) => theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.05;
  shadow-radius: 4px;
  elevation: 2;
`;

export const CheckboxRow = styled(Pressable)`
  flex-direction: row;
  align-items: center;
`;

export const CheckboxIcon = styled(View)<{ checked: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 2px solid
    ${({ theme, checked }) =>
      checked ? theme.colors.primary : theme.colors.divider};
  background-color: ${({ theme, checked }) =>
    checked ? theme.colors.primary : "transparent"};
  align-items: center;
  justify-content: center;
  margin-right: 12px;
`;

export const CheckboxLabel = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  flex: 1;
`;

export const NoShippingAddressContainer = styled(View)`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.error};
  margin-horizontal: 16px;
  margin-bottom: 16px;
`;

export const NoShippingAddressText = styled(Text)`
  color: ${({ theme }) => theme.colors.error};
  font-size: 14px;
  text-align: center;
`;
