import React, { useState } from "react";
import { Alert, ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import Toast from "react-native-toast-message";
import { useFileValidation } from "@/hooks/useFileValidation";
import {
  FormInputGroup,
  FormLabel,
  FormInput,
  ImageUploadButton,
  UploadPlaceholder,
  UploadText,
  UploadedImage,
  ImagePreviewContainer,
  RequiredText,
  CloseButton,
  ImagePreviewRow,
  ImagePreviewItem,
  ImagePreviewImage,
  ImagePreviewCloseButton,
  UploadButtonDisabled,
  FileInfoText,
  FileInfoTextNoMargin,
} from "@/styles/Complaint.styles";

interface OtherBrandFormProps {
  productImages: string[];
  brandName: string;
  onProductImagesChange: (images: string[]) => void;
  onBrandNameChange: (value: string) => void;
}

export const OtherBrandForm: React.FC<OtherBrandFormProps> = ({
  productImages,
  brandName,
  onProductImagesChange,
  onBrandNameChange,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { validateImageFile, getSupportedFormatsDisplay, getMaxFileSize } =
    useFileValidation();
  const [isValidating, setIsValidating] = useState(false);

  const validateAndAddImages = async (
    assets: ImagePicker.ImagePickerAsset[]
  ) => {
    setIsValidating(true);
    const validImages: string[] = [];
    const invalidAssets: ImagePicker.ImagePickerAsset[] = [];

    try {
      // Validate each selected image
      for (const asset of assets) {
        try {
          const validation = await validateImageFile(asset);

          if (validation.isValid) {
            validImages.push(asset.uri);
          } else {
            invalidAssets.push(asset);
            Toast.show({
              type: "error",
              text1: `Invalid file: ${validation.error}`,
            });
          }
        } catch (error) {
          console.error("Image validation error:", error);
          invalidAssets.push(asset);
          Toast.show({
            type: "error",
            text1: "Failed to validate image",
          });
        }
      }

      // Add valid images
      if (validImages.length > 0) {
        const updatedImages = [...productImages, ...validImages].slice(0, 5);
        onProductImagesChange(updatedImages);

        if (validImages.length === assets.length) {
          Toast.show({
            type: "success",
            text1: `${validImages.length} image(s) added successfully!`,
          });
        } else {
          Toast.show({
            type: "info",
            text1: `${validImages.length} of ${assets.length} images added. ${invalidAssets.length} failed validation.`,
          });
        }
      }
    } finally {
      setIsValidating(false);
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== "granted") {
      Alert.alert(t("permission_needed"), t("photo_permission_message"));
      return;
    }

    // Calculate how many more images can be selected
    const remainingSlots = 5 - productImages.length;

    if (remainingSlots <= 0) {
      Toast.show({
        type: "error",
        text1: t("complaint.errors.max_images_reached"),
        text2: t("complaint.errors.max_images_message"),
      });
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
      allowsMultipleSelection: true,
      selectionLimit: remainingSlots,
    });

    if (!result.canceled && result.assets.length > 0) {
      await validateAndAddImages(result.assets);
    }
  };

  const removeImage = (index: number) => {
    const updatedImages = productImages.filter((_, i) => i !== index);
    onProductImagesChange(updatedImages);
  };

  return (
    <>
      <FormInputGroup>
        <FormLabel>
          {t("complaint.form.product_image_required")}
          <RequiredText> *</RequiredText>
        </FormLabel>

        {/* Display uploaded images */}
        {productImages.length > 0 && (
          <ImagePreviewRow>
            {productImages.map((image, index) => (
              <ImagePreviewItem key={index} onPress={() => {}}>
                <ImagePreviewImage
                  source={{ uri: image }}
                  transition={200}
                  contentFit="cover"
                />
                <ImagePreviewCloseButton onPress={() => removeImage(index)}>
                  <Ionicons name="close" size={12} color={theme.colors.white} />
                </ImagePreviewCloseButton>
              </ImagePreviewItem>
            ))}
          </ImagePreviewRow>
        )}

        {/* Upload button */}
        {productImages.length < 5 && (
          <UploadButtonDisabled
            onPress={pickImage}
            disabled={isValidating}
            isValidating={isValidating}
          >
            <UploadPlaceholder>
              {isValidating ? (
                <ActivityIndicator size={24} color={theme.colors.primary} />
              ) : (
                <Ionicons
                  name="camera-outline"
                  size={32}
                  color={theme.colors.gray}
                />
              )}
              <UploadText>
                {isValidating
                  ? "Validating images..."
                  : productImages.length === 0
                  ? t("complaint.tap_to_add_photo")
                  : `${t("complaint.add_more_photos")} (${
                      productImages.length
                    }/5)`}
              </UploadText>
            </UploadPlaceholder>
          </UploadButtonDisabled>
        )}

        <FileInfoText>
          📁 Supported: {getSupportedFormatsDisplay("image")}
        </FileInfoText>
        <FileInfoTextNoMargin>
          📏 Max size: {getMaxFileSize("image")}KB per image
        </FileInfoTextNoMargin>
      </FormInputGroup>

      <FormInputGroup>
        <FormLabel>
          {t("complaint.form.brand_name_required")}
          <RequiredText> *</RequiredText>
        </FormLabel>
        <FormInput
          value={brandName}
          onChangeText={onBrandNameChange}
          placeholder={t("complaint.form.enter_brand_name_placeholder")}
          placeholderTextColor={theme.colors.gray}
        />
      </FormInputGroup>
    </>
  );
};
