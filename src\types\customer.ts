import { ApiResponse } from "./api";

export interface Customer {
  user_id: number;
  customer_id: number;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  gst_number?: string;
  address_line_one?: string | null;
  address_line_two?: string;
  post_code?: string;
  state_id?: number;
  city_id?: number;
  mobile?: string;
  country_code_alpha?: string;
}

export interface GetCustomerListParams {
  search?: string;
  page?: number;
}

export interface CustomerPaginated {
  current_page: number;
  data: Customer[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: any[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface GetCustomerListResponse extends ApiResponse {
  data: CustomerPaginated;
  meta?: any;
}
