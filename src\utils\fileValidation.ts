import * as FileSystem from "expo-file-system";
import { ImagePickerAsset } from "expo-image-picker";

export interface FileValidationSettings {
  supported_file_format: {
    general: string;
    image: string;
  };
  file_size: {
    general: number;
    image: number;
    total_file_size: number;
  };
  supported_file_extension: {
    general: string;
  };
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export const validateFile = async (
  asset: ImagePickerAsset,
  settings: FileValidationSettings,
  fileType: "image" | "general" = "image"
): Promise<ValidationResult> => {
  try {
    // Get file info
    const fileInfo = await FileSystem.getInfoAsync(asset.uri);

    if (!fileInfo.exists) {
      return {
        isValid: false,
        error: "Selected file does not exist",
      };
    }

    // Check file size - use asset.fileSize if available, otherwise use fileInfo.size
    const fileSizeBytes = (asset as any).fileSize || fileInfo.size || 0;
    const maxSizeBytes = settings.file_size[fileType] * 1024; // Convert KB to bytes
    if (fileSizeBytes > maxSizeBytes) {
      return {
        isValid: false,
        error: `File size must be less than ${settings.file_size[fileType]}KB`,
      };
    }

    // Check file format (MIME type)
    const supportedFormats = settings.supported_file_format[fileType]
      .split(",")
      .map((format) => format.trim());

    // Use mimeType if available, otherwise fall back to type
    const assetMimeType = (asset as any).mimeType || asset.type;

    // For image files, if mimeType is not available, try to determine from file extension
    let mimeTypeToCheck = assetMimeType;
    if (!mimeTypeToCheck || mimeTypeToCheck === "image") {
      const fileName = asset.uri.split("/").pop() || "";
      const fileExtension = fileName.split(".").pop()?.toLowerCase();

      if (fileExtension) {
        // Map common extensions to MIME types
        const extensionToMimeType: Record<string, string> = {
          jpg: "image/jpeg",
          jpeg: "image/jpeg",
          png: "image/png",
          heic: "image/heic",
          heif: "image/heic",
          gif: "image/gif",
          webp: "image/webp",
        };

        mimeTypeToCheck =
          extensionToMimeType[fileExtension] || `image/${fileExtension}`;
      }
    }

    if (!supportedFormats.includes(mimeTypeToCheck)) {
      return {
        isValid: false,
        error: `File format not supported. Supported formats: ${settings.supported_file_format[fileType]}`,
      };
    }

    // Check file extension
    const fileName = asset.uri.split("/").pop() || "";
    const fileExtension = fileName.split(".").pop()?.toLowerCase();

    if (fileExtension) {
      const supportedExtensions = settings.supported_file_extension.general
        .split(",")
        .map((ext) => ext.trim().toLowerCase());

      if (!supportedExtensions.includes(fileExtension)) {
        return {
          isValid: false,
          error: `File extension not supported. Supported extensions: ${settings.supported_file_extension.general}`,
        };
      }
    }

    return { isValid: true };
  } catch (error) {
    console.error("File validation error:", error);
    return {
      isValid: false,
      error: "Failed to validate file",
    };
  }
};

export const getFileSizeInKB = (bytes: number): number => {
  return Math.round(bytes / 1024);
};

export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
};
