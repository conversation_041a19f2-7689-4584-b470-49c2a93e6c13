 import React, { useState, useEffect } from "react";
  import { useFormContext, Controller } from "react-hook-form";
  import { ScrollView, View, TouchableOpacity, Text } from "react-native";
  import DateTimePicker from "@react-native-community/datetimepicker";
  import { Picker } from "@react-native-picker/picker";
  import {
    Form,
    ButtonContainer,
    CancelButton,
    ConfirmButton,
    ButtonText,
    DateTimeFieldContainer,
    DateTimeFieldLabel,
    DateTimeFieldTouchable,
    DateTimeFieldText,
    PickerFieldContainer,
    Picker<PERSON><PERSON><PERSON><PERSON>l,
    PickerFieldWrapper,
    QuantityFieldContainer,
    QuantityFieldLabel,
    QuantityButton,
    QuantityValueText,
  } from "./styles";
  import { useAppDispatch, useAppSelector } from "@/store/store";
  import { getProductListAction } from "@/store/actions/product";


  interface EstimationFormProps {
    loading: boolean;
    t: (key: string) => string;
    theme: any;
    onCancel: () => void;
    onSubmit: (data: any) => void;
  }

  export const EstimationForm: React.FC<EstimationFormProps> = ({
    loading,
    t,
    theme,
    onCancel,
    onSubmit,
  }) => {
    const {
      formState: { isValid },
      handleSubmit,
      control,
      setValue,
      watch,
    } = useFormContext();

    const dispatch = useAppDispatch();
    const { products: productList, isLoading: productsLoading } = useAppSelector((state) => state.product);

    useEffect(() => {
      dispatch(getProductListAction({ page: 1 }));
    }, [dispatch]);

    const [showDatePicker, setShowDatePicker] = useState(false);
    const estimateTime = watch("estimate_time");
    const quantity = watch("quantity") || 1;

    return (
      <View style={{ flex: 1 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          <Form>
            <Controller
              control={control}
              name="estimate_time"
              rules={{ required: true }}
              render={({ field: { value, onChange } }) => (
                <DateTimeFieldContainer>
                  <DateTimeFieldLabel>
                    {t("estimation.estimate_time") || "Estimate Time"}
                  </DateTimeFieldLabel>
                  <DateTimeFieldTouchable
                    onPress={() => setShowDatePicker(true)}
                    disabled={loading}
                    $loading={loading}
                  >
                    <DateTimeFieldText $hasValue={!!value}>
                      {value
                        ? new Date(value).toLocaleString()
                        : t("estimation.enter_estimate_time") || "Enter estimate time (e.g., 2-3 days)"}
                    </DateTimeFieldText>
                  </DateTimeFieldTouchable>
                  {showDatePicker && (
                    <DateTimePicker
                      value={value ? new Date(value) : new Date()}
                      mode="datetime"
                      display="default"
                      onChange={(event, selectedDate) => {
                        setShowDatePicker(false);
                        if (selectedDate) {
                          onChange(selectedDate.toISOString());
                        }
                      }}
                    />
                  )}
                </DateTimeFieldContainer>
              )}
            />

            <Controller
              control={control}
              name="product"
              rules={{ required: true }}
              render={({ field: { value, onChange } }) => (
                <PickerFieldContainer>
                  <PickerFieldLabel>
                    {t("estimation.product") || "Product"}
                  </PickerFieldLabel>
                  <PickerFieldWrapper>
                    <Picker
                      selectedValue={value}
                      onValueChange={onChange}
                      enabled={!loading && !productsLoading}
                    >
                      <Picker.Item label={t("estimation.enter_product") || "Select product"} value="" />
                      {productList.map((p) => (
                        <Picker.Item key={p.product_id} label={p.title} value={p.product_id} />
                      ))}
                    </Picker>
                  </PickerFieldWrapper>
                </PickerFieldContainer>
              )}
            />

            <Controller
              control={control}
              name="quantity"
              rules={{ required: true, min: 1 }}
              defaultValue={1}
              render={({ field: { value, onChange } }) => (
                <QuantityFieldContainer>
                  <QuantityFieldLabel>
                    {t("estimation.quantity") || "Quantity"}
                  </QuantityFieldLabel>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <QuantityButton
                      onPress={() => onChange(Math.max(1, (value || 1) - 1))}
                      disabled={loading || (value || 1) <= 1}
                      $disabled={loading || (value || 1) <= 1}
                    >
                      <QuantityValueText>-</QuantityValueText>
                    </QuantityButton>
                    <QuantityValueText $isValue>{value || 1}</QuantityValueText>
                    <QuantityButton
                      onPress={() => onChange((value || 1) + 1)}
                      disabled={loading}
                      $disabled={loading}
                    >
                      <QuantityValueText>+</QuantityValueText>
                    </QuantityButton>
                  </View>
                </QuantityFieldContainer>
              )}
            />
          </Form>
        </ScrollView>

        <ButtonContainer>
          <CancelButton onPress={onCancel} disabled={loading}>
            <ButtonText variant="cancel">
              {t("common.cancel") || "Cancel"}
            </ButtonText>
          </CancelButton>

          <ConfirmButton
            onPress={handleSubmit(onSubmit)}
            disabled={!isValid || loading}
          >
            <ButtonText variant="confirm">
              {loading
                ? t("estimation.submitting") || "Submitting..."
                : t("estimation.submit") || "Submit"}
            </ButtonText>
          </ConfirmButton>
        </ButtonContainer>
      </View>
    );
  };