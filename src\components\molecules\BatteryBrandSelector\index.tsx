import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  SelectionCard,
  SelectionIcon,
  SelectionContent,
  SelectionTitle,
  SelectionSubtitle,
} from "@/styles/Complaint.styles";

interface BatteryBrandOption {
  id: number;
  name: string;
  value: "ozone" | "other";
  icon: string;
}

interface BatteryBrandSelectorProps {
  selectedBrand: "ozone" | "other";
  onBrandSelect: (brand: "ozone" | "other") => void;
}

export const BatteryBrandSelector: React.FC<BatteryBrandSelectorProps> = ({
  selectedBrand,
  onBrandSelect,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const batteryBrandOptions: BatteryBrandOption[] = [
    {
      id: 1,
      name: t("complaint.brands.ozone"),
      value: "ozone",
      icon: "shield-checkmark-outline",
    },
    {
      id: 2,
      name: t("complaint.brands.other"),
      value: "other",
      icon: "globe-outline",
    },
  ];

  return (
    <>
      {batteryBrandOptions.map((option) => (
        <SelectionCard
          key={option.id}
          isSelected={selectedBrand === option.value}
          onPress={() => onBrandSelect(option.value)}
        >
          <SelectionIcon>
            <Ionicons
              name={option.icon as any}
              size={20}
              color={theme.colors.gray}
            />
          </SelectionIcon>
          <SelectionContent>
            <SelectionTitle isSelected={selectedBrand === option.value}>
              {option.name}
            </SelectionTitle>
            <SelectionSubtitle>
              {option.value === "ozone"
                ? t("complaint.brands.ozone_desc")
                : t("complaint.brands.other_desc")}
            </SelectionSubtitle>
          </SelectionContent>
          {selectedBrand === option.value && (
            <Ionicons
              name="checkmark-circle"
              size={24}
              color={theme.colors.primary}
            />
          )}
        </SelectionCard>
      ))}
    </>
  );
};
