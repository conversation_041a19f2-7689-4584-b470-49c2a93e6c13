import { createSlice } from "@reduxjs/toolkit";
import { Customer } from "@/types/customer";
import { getCustomerListAction } from "../actions/customer";
import { logoutAction, logoutLocalAction } from "../actions/auth";

interface CustomerState {
  customers: Customer[] | null;
  customersLoading: boolean;
  customersError: string | null;
  isLoadingMore: boolean;
  current_page: number;
  last_page: number;
  loadedPages: number[];
  filters: any | null;
}

const initialState: CustomerState = {
  customers: [],
  customersLoading: false,
  customersError: null,
  isLoadingMore: false,
  current_page: 1,
  last_page: 1,
  loadedPages: [],
  filters: null,
};

const customerSlice = createSlice({
  name: "customer",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCustomerListAction.pending, (state, action) => {
        // Check if this is pagination (page > 1) or initial load
        const isPagination = action.meta.arg?.page && action.meta.arg.page > 1;
        if (isPagination) {
          state.isLoadingMore = true;
        } else {
          state.customersLoading = true;
        }
        state.customersError = null;
      })
      .addCase(getCustomerListAction.fulfilled, (state, action) => {
        state.customersLoading = false;
        state.isLoadingMore = false;
        state.filters = action.payload.meta;
        const customers = action.payload?.data;
        const pagination = action.payload?.meta;
        if (customers && pagination) {
          state.current_page = pagination.current_page || 1;
          state.last_page = pagination.last_page || 1;

          if (!state.loadedPages.includes(pagination.current_page)) {
            state.loadedPages.push(pagination.current_page);
          }

          if (pagination.current_page === 1) {
            state.customers = customers || [];
            state.loadedPages = [1];
          } else {
            state.customers = [
              ...(state.customers || []),
              ...(customers || []),
            ];
          }
        } else {
          // Fallback if data is undefined
          state.customers = [];
          state.current_page = 1;
          state.last_page = 1;
          state.loadedPages = [1];
        }
      })
      .addCase(getCustomerListAction.rejected, (state, action) => {
        state.customersLoading = false;
        state.isLoadingMore = false;
        state.customersError =
          action.error.message || "Failed to load customers";
      })
      // Logout
      .addCase(logoutAction.fulfilled, () => {
        return initialState;
      })
      // Logout Local
      .addCase(logoutLocalAction.fulfilled, () => {
        return initialState;
      });
  },
});

export const { setFilters } = customerSlice.actions;
export default customerSlice.reducer;
