import { styled } from "@/utils/styled";

export const Overlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  padding-horizontal: 20px;
`;

export const Container = styled.View`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 20px;
  padding: 30px;
  align-items: center;
  max-width: 350px;
  width: 100%;
`;

export const IconContainer = styled.View`
  margin-bottom: 20px;
`;

export const Title = styled.Text`
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15px;
  color: ${({ theme }) => theme.colors.text};
`;

export const Description = styled.Text`
  font-size: 16px;
  text-align: center;
  line-height: 24px;
  margin-bottom: 30px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const ButtonContainer = styled.View`
  width: 100%;
  gap: 12px;
`;

export const Button = styled.Pressable<{ variant?: "primary" | "secondary" }>`
  padding-vertical: 15px;
  padding-horizontal: 20px;
  border-radius: 12px;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme, variant }) =>
    variant === "primary" ? theme.colors.primary : "transparent"};
  border-width: ${({ variant }) => (variant === "secondary" ? 1 : 0)}px;
  border-color: ${({ theme, variant }) =>
    variant === "secondary" ? theme.colors.border : "transparent"};
`;

export const ButtonText = styled.Text<{ variant?: "primary" | "secondary" }>`
  color: ${({ theme, variant }) =>
    variant === "primary" ? theme.colors.white : theme.colors.gray};
  font-size: 16px;
  font-weight: ${({ variant }) => (variant === "primary" ? 600 : 500)};
`;

export const FooterText = styled.Text`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  margin-top: 20px;
`;
