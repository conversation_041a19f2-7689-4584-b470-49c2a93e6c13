import React from "react";
import { <PERSON><PERSON>View, View } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { AddressCard } from "@/components/molecules";
import { CartButton, LoadingOverlay } from "@/components/atoms";
import { useTheme } from "@/hooks/useTheme";
import {
  Container,
  Header,
  Title,
  Subtitle,
  SectionHeader,
  SectionIcon,
  SectionTitle,
  EmptyState,
  EmptyIcon,
  EmptyTitle,
  EmptyDescription,
  CheckboxContainer,
  CheckboxRow,
  CheckboxIcon,
  CheckboxLabel,
  NoShippingAddressContainer,
  NoShippingAddressText,
} from "./styles";
import { VendorDetails } from "@/types/vendor";
import { useAppSelector } from "@/store/store";

interface Address {
  id: number;
  contact_person_name: string;
  address_line_one: string;
  address_line_two?: string;
  post_code: string;
  phone?: string;
  billing_shipping: string;
}

interface AddressSelectionProps {
  addresses: Address[];
  selectedBillingId: number | null;
  selectedShippingId: number | null;
  onSelectBilling: (address: Address) => void;
  onSelectShipping: (address: Address) => void;
  onEditAddress: (addressId: number, selectedVendor?: VendorDetails) => void;
  useBillingAsShipping?: boolean;
  onToggleBillingAsShipping?: (value: boolean) => void;
  selectedVendor?: VendorDetails | null;
  onAddNewAddress?: () => void;
  isLoading?: boolean;
  hasMoreAddresses?: boolean;
  onLoadMore?: () => void;
}

/**
 * AddressSelection - Organism component for address selection
 * Displays billing and shipping addresses with selection functionality
 */
const AddressSelection: React.FC<AddressSelectionProps> = ({
  addresses,
  selectedBillingId,
  selectedShippingId,
  onSelectBilling,
  onSelectShipping,
  onEditAddress,
  useBillingAsShipping = true,
  onToggleBillingAsShipping,
  selectedVendor,
  onAddNewAddress,
  isLoading = false,
  hasMoreAddresses = false,
  onLoadMore,
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useAppSelector((state) => state.auth);
  const billingAddresses = Array.isArray(addresses)
    ? addresses.filter((addr) => addr.billing_shipping === "1")
    : [];
  const shippingAddresses = Array.isArray(addresses)
    ? addresses.filter((addr) => addr.billing_shipping === "2")
    : [];

  const handleAddNewAddress = () => {
    if (onAddNewAddress) {
      onAddNewAddress();
    } else {
      if (selectedVendor) {
        router.push(
          `/(protected)/add-address?vendorId=${selectedVendor.vendor_id}&userId=${selectedVendor.user_id}`
        );
      } else {
        router.push(`/(protected)/add-address?userId=${user?.id}`);
      }
    }
  };

  if (isLoading) {
    return <LoadingOverlay isLoading={true} size="large" />;
  }

  if (!Array.isArray(addresses) || addresses.length === 0) {
    return (
      <Container>
        <EmptyState>
          <EmptyIcon>
            <Ionicons
              name="location-outline"
              size={32}
              color={theme.colors.primary}
            />
          </EmptyIcon>
          <EmptyTitle>{t("addressSelection.noAddressesFound")}</EmptyTitle>
          <EmptyDescription>
            {t("addressSelection.noAddressesDescription")}
          </EmptyDescription>
        </EmptyState>
      </Container>
    );
  }

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Header>
          <Title>{t("addressSelection.title")}</Title>
          <Subtitle>{t("addressSelection.subtitle")}</Subtitle>
        </Header>

        <CartButton
          title={t("addressSelection.addNewAddress")}
          variant="primary"
          icon="add-circle"
          onPress={handleAddNewAddress}
          fullWidth
          style={{ marginBottom: 16 }}
        />

        {onToggleBillingAsShipping && (
          <CheckboxContainer>
            <CheckboxRow
              onPress={() => onToggleBillingAsShipping(!useBillingAsShipping)}
            >
              <CheckboxIcon checked={useBillingAsShipping}>
                {useBillingAsShipping && (
                  <Ionicons
                    name="checkmark"
                    size={16}
                    color={theme.colors.white}
                  />
                )}
              </CheckboxIcon>
              <CheckboxLabel>
                {t("addressSelection.useBillingAsShipping")}
              </CheckboxLabel>
            </CheckboxRow>
          </CheckboxContainer>
        )}

        {billingAddresses.length > 0 && (
          <>
            <SectionHeader>
              <SectionIcon>
                <Ionicons
                  name="receipt-outline"
                  size={20}
                  color={theme.colors.primary}
                />
              </SectionIcon>
              <SectionTitle>
                {t("addressSelection.billingAddresses")}
              </SectionTitle>
            </SectionHeader>

            {billingAddresses.map((address) => (
              <AddressCard
                key={`billing-${address.id}`}
                address={address}
                isSelected={selectedBillingId === address.id}
                onSelect={() => onSelectBilling(address)}
                onEdit={() => onEditAddress(address.id, selectedVendor)}
                type="billing"
              />
            ))}
          </>
        )}

        {!useBillingAsShipping && (
          <>
            <SectionHeader>
              <SectionIcon>
                <Ionicons
                  name="location-outline"
                  size={20}
                  color={theme.colors.success}
                />
              </SectionIcon>
              <SectionTitle>
                {t("addressSelection.shippingAddresses")}
              </SectionTitle>
            </SectionHeader>

            {shippingAddresses.length > 0 ? (
              shippingAddresses.map((address) => (
                <AddressCard
                  key={`shipping-${address.id}`}
                  address={address}
                  isSelected={selectedShippingId === address.id}
                  onSelect={() => onSelectShipping(address)}
                  onEdit={() => onEditAddress(address.id, selectedVendor)}
                  type="shipping"
                />
              ))
            ) : (
              <NoShippingAddressContainer>
                <NoShippingAddressText>
                  {t("addressSelection.noShippingAddresses")}
                </NoShippingAddressText>
              </NoShippingAddressContainer>
            )}
          </>
        )}

        {useBillingAsShipping && (
          <SectionHeader>
            <SectionIcon>
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={theme.colors.success}
              />
            </SectionIcon>
            <View style={{ flex: 1 }}>
              <SectionTitle style={{ color: theme.colors.success }}>
                {t("addressSelection.shippingSameAsBilling")}
              </SectionTitle>
            </View>
          </SectionHeader>
        )}

        {hasMoreAddresses && onLoadMore && (
          <View style={{ padding: 16 }}>
            <CartButton
              title={isLoading ? "Loading..." : "Load More Addresses"}
              variant="secondary"
              icon="add-circle"
              onPress={onLoadMore}
              fullWidth
              disabled={isLoading}
            />
          </View>
        )}
      </ScrollView>
    </Container>
  );
};

export default AddressSelection;
