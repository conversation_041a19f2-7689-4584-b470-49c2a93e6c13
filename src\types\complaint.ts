import { ApiResponse } from "./api";

export enum ComplaintStatusEnum {
  Pending = 1,
  InReview = 2,
  EstimationSent = 3,
  Approved = 4,
  RepairInProgress = 5,
  Resolved = 6,
  Rejected = 7,
  Assigned = 8,
}
export interface ComplaintEstimationProduct {
  slug: string;
  quantity: number;
}

export interface ComplaintEstimation {
  slug: string;
  estimate_time: string;
  products?: ComplaintEstimationProduct[];
}

export interface Complaint {
  id: string;
  slug: string;
  status: ComplaintStatusEnum;
  description: string;
  serial_number: string;
  created_at: string;
  updated_at?: string;
  resolution?: string;
  resolved_at?: string;
  image?: string;
  warranty_status?: "in_warranty" | "out_of_warranty" | "partial_warranty";
  repair_cost?: number;
  estimation?: ComplaintEstimation;
}

export interface ComplaintListResponse extends ApiResponse {
  data: Complaint[];
  meta: ComplaintListPayload;
}

export interface ComplaintListPayload {
  page?: number;
  search?: string;
  order_by?: number;
  sort_order?: number;
  complaint_type?: number;
  status?: number;
}

export interface CreateComplaintPayload {
  complaint_for: 1 | 2;
  complaint_type: 1 | 2;
  first_name?: string;
  last_name?: string;
  country_code_alpha?: string;
  mobile?: string;
  user_id?: number;
  description?: string;
  complaint_issue_id?: number;
  product_qr_code_slug?: string;
  serial_no?: string;
  product_serial_photo?: { image: string }[];
  brand_name?: string;
  latitude: number;
  longitude: number;
  billing_address_id: number;
  shipping_address_id: number;
}

export interface ComplaintStatus extends ApiResponse {
  id: number;
  label: string;
}

export interface ComplaintState {
  complaints: Complaint[];
  loading: boolean;
  error: string | null;
  complaintsLoading: boolean;
  isLoadingMore: boolean;
  filters: ComplaintListPayload | null;
  current_page: number;
  last_page: number;
  loadedPages: number[];
  complaintStatus: ComplaintStatus[];
  complaintIssues: ComplaintIssue[];
  servicePersonList: ServicePerson[];
  servicePersonCurrentPage: number;
  servicePersonLastPage: number;
  servicePersonLoadedPages: number[];
  estimationDetails: ComplaintEstimation | null;
  estimationLoading: boolean;
  partList: Part[];
  partListCurrentPage: number;
  partListLastPage: number;
  partListLoadedPages: number[];
  partListLoading: boolean;
  partListLoadingMore: boolean;
}

export enum ComplaintFor {
  VENDOR = 1,
  CUSTOMER = 2,
}

export interface ComplaintIssueListResponse extends ApiResponse {
  data: ComplaintIssue[];
}

export interface ComplaintIssue {
  id: number;
  name: string;
}

export interface ServicePerson {
  user_id: number;
  service_person_id: number;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  address_line_one: string;
  address_line_two: string;
  post_code: string;
  state_id: number;
  city_id: number;
  mobile: string;
  country_code_alpha: string;
}

export interface ServicePersonListResponse extends ApiResponse {
  data: ServicePerson[];
}

export interface ServicePersonListPayload {
  page?: number;
  search?: string;
}

export interface AssignComplaintPayload {
  slug: string;
  assign_to_id: number;
}

export interface Part {
  part_id: number;
  slug: string;
  name: string;
  sort_order: number;
}
