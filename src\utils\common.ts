import { scanFromURLAsync } from "expo-camera";
import { CartList, OrderStatusEnum } from "../types/order";
import { ComplaintStatusEnum } from "../types/complaint";

export const OTP_LENGTH = 6;
export const RESEND_TIMEOUT = 30;
export const sleep = <T>(seconds: number, value: T): Promise<T> => {
  return new Promise((resolve) =>
    setTimeout(() => resolve(value), seconds * 1000)
  );
};

export const gstCalculation = (cartList: CartList) => {
  const { cartItems, cgst_percentage, sgst_percentage, igst_percentage } =
    cartList;
  let subTotal = 0;

  for (const item of cartItems) {
    const price = parseFloat(item.price);
    subTotal += price * item.quantity;
  }

  const cgstAmount = (subTotal * cgst_percentage) / 100;
  const sgstAmount = (subTotal * sgst_percentage) / 100;
  const igstAmount = (subTotal * igst_percentage) / 100;

  const totalAmount = subTotal + cgstAmount + sgstAmount + igstAmount;

  return {
    sub_total: Math.round(subTotal).toString(),
    cgst_total: Math.round(cgstAmount).toString(),
    sgst_total: Math.round(sgstAmount).toString(),
    igst_total: Math.round(igstAmount).toString(),
    total_amount: Math.round(totalAmount).toString(),
  };
};
export const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return dateString;
  }
};

export const sortOptions = [
  { label: "Title: A-Z", order_by: 1, sort_order: 1 },
  { label: "Title: Z-A", order_by: 1, sort_order: 2 },
  { label: "Price: Low to High", order_by: 2, sort_order: 1 },
  { label: "Price: High to Low", order_by: 2, sort_order: 2 },
];

export const orderSortOptions = [
  { id: 1, label: "Order Number (Newest)", order_by: 1, sort_order: 1 },
  { id: 2, label: "Order Number (Oldest)", order_by: 1, sort_order: 2 },
  { id: 3, label: "Order Date (Newest)", order_by: 2, sort_order: 1 },
  { id: 4, label: "Order Date (Oldest)", order_by: 2, sort_order: 2 },
  { id: 5, label: "Order Total (Lowest)", order_by: 3, sort_order: 1 },
  { id: 6, label: "Order Total (Highest)", order_by: 3, sort_order: 2 },
];

export const vendorSortOptions = [
  { id: 1, label: "Name (A-Z)", order_by: 1, sort_by: 1 },
  { id: 2, label: "Name (Z-A)", order_by: 2, sort_by: 1 },
  { id: 3, label: "Email (A-Z)", order_by: 1, sort_by: 2 },
  { id: 4, label: "Email (Z-A)", order_by: 2, sort_by: 2 },
];

export const orderTypeOptions = [
  { id: 1, label: "Direct" },
  { id: 2, label: "Complient" },
];

export const orderStatusList = [
  { id: OrderStatusEnum.Pending, label: "Pending" },
  { id: OrderStatusEnum.Approved, label: "Approved" },
  { id: OrderStatusEnum.InDispatch, label: "In Dispatch" },
  { id: OrderStatusEnum.Dispatched, label: "Dispatched" },
  { id: OrderStatusEnum.Received, label: "Received" },
  { id: OrderStatusEnum.Cancelled, label: "Cancelled" },
];

export const vendorStatusList = [
  { id: 0, label: "Unauthorised" },
  { id: 1, label: "Authorised" },
];

export const complaintStatusList = [
  { id: ComplaintStatusEnum.Pending, label: "Pending" },
  { id: ComplaintStatusEnum.InReview, label: "In Review" },
  { id: ComplaintStatusEnum.EstimationSent, label: "Estimation Sent" },
  { id: ComplaintStatusEnum.Approved, label: "Approved" },
  { id: ComplaintStatusEnum.RepairInProgress, label: "Repair In Progress" },
  { id: ComplaintStatusEnum.Resolved, label: "Resolved" },
  { id: ComplaintStatusEnum.Rejected, label: "Rejected" },
];

export const complaintSortOptions = [
  { id: 1, label: "Complaint Number (Newest)", order_by: 1, sort_order: 1 },
  { id: 2, label: "Complaint Number (Oldest)", order_by: 1, sort_order: 2 },
  { id: 3, label: "Date (Newest)", order_by: 2, sort_order: 1 },
  { id: 4, label: "Date (Oldest)", order_by: 2, sort_order: 2 },
];

export const complaintTypeOptions = [
  { id: 1, label: "Ozone Product" },
  { id: 2, label: "3rd-Party Product" },
];

export const objectToFormdata = (obj: any) => {
  const formData = new FormData();
  Object.keys(obj).forEach((key) => formData.append(key, obj[key]));
  return formData;
};

export const createFormDataWithImages = (
  payload: any,
  imageConfig?: {
    fieldName: string;
    images: Array<{ image: string }>;
    fileNamePrefix?: string;
  }
) => {
  // Create payload without images
  const payloadWithoutImages = { ...payload };
  if (imageConfig) {
    delete payloadWithoutImages[imageConfig.fieldName];
  }

  // Convert to FormData
  const formData = objectToFormdata(payloadWithoutImages);

  // Add images if provided
  if (imageConfig && imageConfig.images.length > 0) {
    imageConfig.images.forEach((imageObj, index) => {
      const imageUri = imageObj.image;
      const fileName =
        imageUri.split("/").pop() ||
        `${imageConfig.fileNamePrefix || "image"}_${index}.jpg`;
      const mimeType = "image/jpeg";

      formData.append(`${imageConfig.fieldName}[${index}]`, {
        uri: imageUri,
        name: fileName,
        type: mimeType,
      } as any);
    });
  }

  return formData;
};

export const formatAmount = (value: string | number): string => {
  try {
    // Convert string with commas like "5,950.00" to number
    const cleaned = typeof value === "string" ? value.replace(/,/g, "") : value;
    const numValue = Number(cleaned);
    if (isNaN(numValue)) return "0.00";

    return numValue.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  } catch {
    return "0.00";
  }
};
export const decodeDecimal = (value: string | number) => {
  if (typeof value === "number") return value;
  if (typeof value === "string") {
    // Remove all commas, then parse to float
    return parseFloat(value.replaceAll(",", ""));
  }
  return NaN;
};
export const complaintForOptions = [
  {
    id: 1,
    name: "Vendor",
    value: "vendor",
    icon: "business-outline",
  },
  {
    id: 2,
    name: "Customer",
    value: "customer",
    icon: "person-outline",
  },
];
export const scanQrImage = async (uri: string): Promise<string | null> => {
  const response = await scanFromURLAsync(uri);

  if (response.length) {
    return response[0].data;
  }
  return null;
};
