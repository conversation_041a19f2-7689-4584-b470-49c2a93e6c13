import { configureStore } from "@reduxjs/toolkit";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { combineReducers } from "redux";

import authReducer from "./slices/authSlice";
import settingsReducer from "./slices/settingsSlice";
import productReducer from "./slices/productSlice";
import complaintsReducer from "./slices/complaintSlice";
import ordersReducer from "./slices/orderSlice";
import notificationsReducer from "./slices/notificationsSlice";
import { useDispatch, useSelector } from "react-redux";
import { TypedUseSelectorHook } from "react-redux";
import vendorReducer from "./slices/vendorSlice";
import customerReducer from "./slices/customerSlice";

const persistConfig = {
  key: "root",
  storage: AsyncStorage,
  whitelist: [
    "auth",
    "settings",
    "vendor",
    "customer",
    "complaints",
    "orders",
    "notifications",
    "product",
  ],
};

const rootReducer = combineReducers({
  auth: authReducer,
  settings: settingsReducer,
  product: productReducer,
  complaints: complaintsReducer,
  orders: ordersReducer,
  notifications: notificationsReducer,
  vendor: vendorReducer,
  customer: customerReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
      immutableCheck: false,
    }),
  devTools: __DEV__,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
