import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetFlatList, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { ProductType } from "@/types/product";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  Title,
  SearchContainer,
  SearchInput,
  ProductItem,
  ProductInfo,
  ProductName,
  ProductSku,
  ProductPrice,
  EmptyContainer,
  EmptyText,
  StyledBottomSheetView,
  ItemsCountText,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getProductListAction } from "@/store/actions/product";
import { useDebounce } from "@/utils/useDebounce";
import { View } from "react-native";

interface ProductSelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (product: ProductType) => void;
  products?: ProductType[];
  isLoading?: boolean;
}

const ProductSelectionBottomSheet: React.FC<
  ProductSelectionBottomSheetProps
> = ({
  isVisible,
  onClose,
  onSelect,
  products: propProducts,
  isLoading: propIsLoading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const {
    products: reduxProducts,
    isLoading: isProductListLoading,
    current_page,
    last_page,
  } = useAppSelector((state) => state.product);

  const debouncedSearch = useDebounce(query, 400);

  const products = Array.isArray(propProducts) ? propProducts : reduxProducts;

  const isLoading = isProductListLoading || propIsLoading;

  const fetchProducts = useCallback(
    async (params: { page: number; search?: string }) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        await dispatch(getProductListAction(params)).unwrap();
      } catch (error: any) {
        console.error("fetchProducts error:", error);
      } finally {
        setIsRefreshing(false);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    if (isVisible && products.length === 0) {
      fetchProducts({ page: 1 });
    }
  }, [isVisible]);

  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length >= 3) {
      fetchProducts({
        page: 1,
        search: debouncedSearch,
      });
    } else if (debouncedSearch === "") {
      fetchProducts({ page: 1 });
    }
  }, [debouncedSearch, fetchProducts]);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["80%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const handleLoadMore = useCallback(() => {
    if (current_page < last_page) {
      const payload: any = {
        page: current_page + 1,
      };
      if (debouncedSearch) payload.search = debouncedSearch;
      fetchProducts(payload);
    }
  }, [current_page, last_page, debouncedSearch, fetchProducts]);

  const handleProductSelect = useCallback(
    (product: ProductType) => {
      onSelect(product);
      onClose();
    },
    [onSelect, onClose]
  );

  const renderProductItem = useCallback(
    ({ item }: { item: ProductType }) => (
      <ProductItem onPress={() => handleProductSelect(item)}>
        <ProductInfo>
          <ProductName>{item.title}</ProductName>
          <ProductSku>SKU: {item.sku}</ProductSku>
          <ProductPrice>₹{item.price}</ProductPrice>
        </ProductInfo>
      </ProductItem>
    ),
    [handleProductSelect]
  );

  const renderEmptyComponent = useCallback(() => {
    if (isLoading && !isRefreshing) {
      return <LoadingOverlay isLoading={true} size="large" />;
    }

    return (
      <EmptyContainer>
        <EmptyText>
          {query
            ? t("common.no_results_found")
            : t("common.no_products_available")}
        </EmptyText>
      </EmptyContainer>
    );
  }, [isLoading, isRefreshing, query, t]);

  const renderFooter = useCallback(() => {
    if (current_page < last_page) {
      return (
        <View style={{ paddingVertical: 30 }}>
          <LoadingOverlay isLoading={true} size="small" />
        </View>
      );
    }
    return null;
  }, [current_page, last_page]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      backdropComponent={renderBackdrop}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enableDynamicSizing={false}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <Title>{t("estimation.select_product")}</Title>

        <SearchContainer>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={t("estimation.search_products")}
            value={query}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.text}
          />
        </SearchContainer>

        <ItemsCountText>
          {products.length} {t("common.products")}
        </ItemsCountText>

        <BottomSheetFlatList
          data={products}
          keyExtractor={(item) => item.product_id.toString()}
          renderItem={renderProductItem}
          ListEmptyComponent={renderEmptyComponent}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          showsVerticalScrollIndicator={false}
          refreshing={isRefreshing}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default ProductSelectionBottomSheet;
