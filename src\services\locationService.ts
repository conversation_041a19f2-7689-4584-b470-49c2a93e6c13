import * as Location from "expo-location";
import { Al<PERSON>, Linking, Platform } from "react-native";

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
}

export interface LocationPermissionResult {
  success: boolean;
  coordinates?: LocationCoordinates;
  error?: string;
  permissionStatus?: Location.PermissionStatus;
}

export class LocationService {
  /**
   * Request location permission and get coordinates
   * @returns Promise<LocationPermissionResult>
   */
  static async requestLocationWithPermission(): Promise<LocationPermissionResult> {
    try {
      // Check if location services are enabled
      const isLocationEnabled = await Location.hasServicesEnabledAsync();
      if (!isLocationEnabled) {
        return {
          success: false,
          error:
            "Location services are disabled. Please enable location services in your device settings.",
        };
      }

      // Check current permission status
      const { status: currentStatus } =
        await Location.getForegroundPermissionsAsync();

      let permissionStatus = currentStatus;

      // Request permission if not determined or denied
      if (
        currentStatus === Location.PermissionStatus.UNDETERMINED ||
        currentStatus === Location.PermissionStatus.DENIED
      ) {
        const { status } = await Location.requestForegroundPermissionsAsync();
        permissionStatus = status;

        if (status === Location.PermissionStatus.DENIED) {
          return {
            success: false,
            error:
              "Location permission denied. Location is required for complaint submission.",
            permissionStatus,
          };
        }
      }

      // If permission granted, get current location
      if (permissionStatus === Location.PermissionStatus.GRANTED) {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 10000,
          distanceInterval: 10,
        });

        const coordinates: LocationCoordinates = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          timestamp: location.timestamp,
        };

        return {
          success: true,
          coordinates,
          permissionStatus,
        };
      }

      return {
        success: false,
        error: "Unable to get location coordinates.",
        permissionStatus,
      };
    } catch (error) {
      console.error("LocationService error:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown location error",
      };
    }
  }

  /**
   * Show settings alert for location permission
   */
  static showLocationSettingsAlert(): void {
    Alert.alert(
      "Location Permission Required",
      "Location access is required for complaint submission. Please enable location access in your device settings.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Open Settings",
          onPress: () => {
            if (Platform.OS === "ios") {
              Linking.openURL("app-settings:");
            } else {
              Linking.openSettings();
            }
          },
        },
      ]
    );
  }

  /**
   * Check if location permission is granted
   */
  static async checkLocationPermission(): Promise<Location.PermissionStatus> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      return status;
    } catch (error) {
      console.error("Error checking location permission:", error);
      return Location.PermissionStatus.DENIED;
    }
  }

  /**
   * Get current location if permission is already granted
   */
  static async getCurrentLocation(): Promise<LocationCoordinates | null> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();

      if (status !== Location.PermissionStatus.GRANTED) {
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 10000,
        distanceInterval: 10,
      });

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        timestamp: location.timestamp,
      };
    } catch (error) {
      console.error("Error getting current location:", error);
      return null;
    }
  }
}
