import React from "react";
import ImageViewing from "react-native-image-viewing";

export interface ImageGalleryModalProps {
  visible: boolean;
  images: Array<string | { uri: string }>;
  initialIndex?: number;
  onRequestClose: () => void;
}

/**
 * ImageGalleryModal - A reusable modal image gallery with zoom and swipe support.
 * Usage: <ImageGalleryModal visible={...} images={[...]} initialIndex={0} onRequestClose={...} />
 */
const ImageGalleryModal: React.FC<ImageGalleryModalProps> = ({
  visible,
  images,
  initialIndex = 0,
  onRequestClose,
}) => {
  // Normalize images to { uri } format for the library
  const formattedImages = images.map((img) =>
    typeof img === "string" ? { uri: img } : img
  );

  return (
    <ImageViewing
      images={formattedImages}
      imageIndex={initialIndex}
      visible={visible}
      onRequestClose={onRequestClose}
      // Optional: you can customize header/footer here if needed
    />
  );
};

export default ImageGalleryModal;
