import React from "react";
import { useTranslation } from "react-i18next";
import {
  Card,
  Title,
  Label,
  Value,
  StatusBadge,
  StatusText,
  ImageContainer,
  ComplaintCardImage,
  MorePhotosText,
} from "@/styles/Complaint.styles";

interface ComplaintCardProps {
  complaint: any;
  getStatusLabel: (status: number) => string | undefined;
  onPress?: (complaint: any) => void;
}

const ComplaintCard: React.FC<ComplaintCardProps> = ({
  complaint,
  getStatusLabel,
  onPress,
}) => {
  const { t } = useTranslation();

  // Determine what to show as description/issue
  const descriptionText = complaint.description
    ? complaint.description
    : complaint.complaint_issue?.name || "No description";

  // Get up to 2 product photos for card display
  const displayPhotos = complaint.product_serial_photo?.slice(0, 2) || [];

  const handleCardPress = () => {
    if (onPress) {
      onPress(complaint);
    }
  };

  return (
    <Card onPress={handleCardPress}>
      <Title>{complaint?.complaint_number}</Title>

      <Label>{t("complaint.created_by")}</Label>
      <Value>{complaint.created_by?.name}</Value>

      <Label>{t("complaint.type")}</Label>
      <Value>
        {complaint.complaint_type === "1"
          ? "Ozone Battery"
          : "3rd Party Battery"}
      </Value>

      <Label>
        {complaint.description
          ? t("complaint.description")
          : t("complaint.issue")}
      </Label>
      <Value>{descriptionText}</Value>

      {complaint.product_qr_code?.serial_no && (
        <>
          <Label>{t("complaint.serial_number")}</Label>
          <Value>{complaint.product_qr_code.serial_no}</Value>
        </>
      )}

      {complaint.brand_name && (
        <>
          <Label>{t("complaint.brand_name")}</Label>
          <Value>{complaint.brand_name}</Value>
        </>
      )}

      {displayPhotos.length > 0 && (
        <>
          <Label>{t("complaint.product_photos")}</Label>
          <ImageContainer>
            {displayPhotos.map((photo: any, index: number) => (
              <ComplaintCardImage
                key={photo.id}
                source={{ uri: photo.url }}
                contentFit="contain"
              />
            ))}
            {complaint.product_serial_photo?.length > 2 && (
              <MorePhotosText>
                +{complaint.product_serial_photo.length - 2} more
              </MorePhotosText>
            )}
          </ImageContainer>
        </>
      )}

      <Label>{t("complaint.status")}</Label>
      <StatusBadge status={Number(complaint.status)}>
        <StatusText status={Number(complaint.status)}>
          {getStatusLabel(complaint.status)}
        </StatusText>
      </StatusBadge>

      <Label>{t("complaint.created_at")}</Label>
      <Value>{complaint.created_at}</Value>
    </Card>
  );
};

export default ComplaintCard;
