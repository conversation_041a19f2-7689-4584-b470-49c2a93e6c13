import React, { useState } from "react";
import { View, Text, ActivityIndicator, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import Toast from "react-native-toast-message";
import { useFileValidation } from "@/hooks/useFileValidation";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  FormInputGroup,
  FormLabel,
  FormInput,
  QRCodeSection,
  QRCodeButton,
  QRCodeButtonText,
  QRCodeButtonContent,
  QRCodeButtonTextContainer,
  QRCodeButtonSubtitle,
  QRCodeButtonTitle,
  RequiredText,
  ImagePreviewRow,
  ImagePreviewItem,
  ImagePreviewImage,
  ImagePreviewCloseButton,
  UploadButtonDisabled,
  UploadPlaceholder,
  UploadText,
  FileInfoTextNoMargin,
  FileInfoText,
} from "@/styles/Complaint.styles";
import { QRScannerModal } from "../QRScannerModal";

interface OzoneBatteryFormProps {
  serialNumber: string;
  onQRCodeChange: (value: string) => void;
  onSerialNumberChange: (value: string) => void;
  isLoadingQRCode?: boolean;
  productImages: string[];
  brandName: string;
  onProductImagesChange: (images: string[]) => void;
  onBrandNameChange: (value: string) => void;
}

export const OzoneBatteryForm: React.FC<OzoneBatteryFormProps> = ({
  serialNumber,
  onQRCodeChange,
  onSerialNumberChange,
  isLoadingQRCode = false,
  productImages,
  brandName,
  onProductImagesChange,
  onBrandNameChange,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [showScanner, setShowScanner] = React.useState(false);
  const { validateImageFile, getSupportedFormatsDisplay, getMaxFileSize } =
    useFileValidation();
  const [isValidating, setIsValidating] = useState(false);

  const handleScanQRCode = () => {
    if (!isLoadingQRCode) {
      setShowScanner(true);
    }
  };

  const validateAndAddImages = async (
    assets: ImagePicker.ImagePickerAsset[]
  ) => {
    setIsValidating(true);
    const validImages: string[] = [];
    const invalidAssets: ImagePicker.ImagePickerAsset[] = [];

    try {
      for (const asset of assets) {
        try {
          const validation = await validateImageFile(asset);

          if (validation.isValid) {
            validImages.push(asset.uri);
          } else {
            invalidAssets.push(asset);
            Toast.show({
              type: "error",
              text1: `Invalid file: ${validation.error}`,
            });
          }
        } catch (error) {
          console.error("Image validation error:", error);
          invalidAssets.push(asset);
          Toast.show({
            type: "error",
            text1: "Failed to validate image",
          });
        }
      }

      if (validImages.length > 0) {
        const updatedImages = [...productImages, ...validImages].slice(0, 5);
        onProductImagesChange(updatedImages);

        if (validImages.length === assets.length) {
          Toast.show({
            type: "success",
            text1: `${validImages.length} image(s) added successfully!`,
          });
        } else {
          Toast.show({
            type: "info",
            text1: `${validImages.length} of ${assets.length} images added. ${invalidAssets.length} failed validation.`,
          });
        }
      }
    } finally {
      setIsValidating(false);
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== "granted") {
      Alert.alert(t("permission_needed"), t("photo_permission_message"));
      return;
    }

    const remainingSlots = 5 - productImages.length;

    if (remainingSlots <= 0) {
      Toast.show({
        type: "error",
        text1: t("complaint.errors.max_images_reached"),
        text2: t("complaint.errors.max_images_message"),
      });
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
      allowsMultipleSelection: true,
      selectionLimit: remainingSlots,
    });

    if (!result.canceled && result.assets.length > 0) {
      await validateAndAddImages(result.assets);
    }
  };

  const removeImage = (index: number) => {
    const updatedImages = productImages.filter((_, i) => i !== index);
    onProductImagesChange(updatedImages);
  };

  return (
    <>
      <QRCodeSection>
        <QRCodeButton onPress={handleScanQRCode} disabled={isLoadingQRCode}>
          <QRCodeButtonContent>
            {isLoadingQRCode ? (
              <ActivityIndicator size={40} color={theme.colors.primary} />
            ) : (
              <Ionicons
                name="qr-code-outline"
                size={40}
                color={theme.colors.primary}
              />
            )}
            <QRCodeButtonTextContainer>
              <QRCodeButtonTitle>
                {isLoadingQRCode
                  ? t("common.loading", "Loading...")
                  : t("complaint.form.scan_qr_code")}
              </QRCodeButtonTitle>
              <QRCodeButtonSubtitle>
                {isLoadingQRCode
                  ? t(
                      "complaint.form.processing_qr_code",
                      "Processing QR code..."
                    )
                  : t(
                      "complaint.form.scan_qr_code_subtitle",
                      "Scan QR code to raise a Complaint"
                    )}
              </QRCodeButtonSubtitle>
            </QRCodeButtonTextContainer>
          </QRCodeButtonContent>
        </QRCodeButton>
        {showScanner && (
          <QRScannerModal
            visible={showScanner}
            onClose={() => setShowScanner(false)}
            onScanned={onQRCodeChange}
          />
        )}
      </QRCodeSection>

      <FormInputGroup>
        <FormLabel>{t("complaint.serial_number")}</FormLabel>
        <FormInput
          value={serialNumber}
          onChangeText={onSerialNumberChange}
          placeholder={t("complaint.form.enter_battery_serial")}
          placeholderTextColor={theme.colors.gray}
        />
      </FormInputGroup>

      <FormInputGroup>
        <FormLabel>
          {t("complaint.form.product_image_required")}
          <RequiredText> *</RequiredText>
        </FormLabel>

        {productImages.length > 0 && (
          <ImagePreviewRow>
            {productImages.map((image, index) => (
              <ImagePreviewItem key={index} onPress={() => {}}>
                <ImagePreviewImage
                  source={{ uri: image }}
                  transition={200}
                  contentFit="cover"
                />
                <ImagePreviewCloseButton onPress={() => removeImage(index)}>
                  <Ionicons name="close" size={12} color={theme.colors.white} />
                </ImagePreviewCloseButton>
              </ImagePreviewItem>
            ))}
          </ImagePreviewRow>
        )}

        {productImages.length < 5 && (
          <UploadButtonDisabled
            onPress={pickImage}
            disabled={isValidating}
            isValidating={isValidating}
          >
            <UploadPlaceholder>
              {isValidating ? (
                <ActivityIndicator size={24} color={theme.colors.primary} />
              ) : (
                <Ionicons
                  name="camera-outline"
                  size={32}
                  color={theme.colors.gray}
                />
              )}
              <UploadText>
                {isValidating
                  ? "Validating images..."
                  : productImages.length === 0
                  ? t("complaint.tap_to_add_photo")
                  : `${t("complaint.add_more_photos")} (${
                      productImages.length
                    }/5)`}
              </UploadText>
            </UploadPlaceholder>
          </UploadButtonDisabled>
        )}

        <FileInfoText>
          📁 Supported: {getSupportedFormatsDisplay("image")}
        </FileInfoText>
        <FileInfoTextNoMargin>
          📏 Max size: {getMaxFileSize("image")}KB per image
        </FileInfoTextNoMargin>
      </FormInputGroup>
    </>
  );
};
