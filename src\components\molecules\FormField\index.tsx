import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import { TextInputProps } from "react-native";
import Input from "../../atoms/Input";
import Text from "../../atoms/Text";
import { Container } from "./styles";

interface FormFieldProps extends TextInputProps {
  name: string;
  label?: string;
  rules?: any;
  inputComponent?: React.ComponentType<any>;
}

const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  rules,
  inputComponent,
  ...inputProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const error = errors[name];
  const InputComponent = inputComponent || Input;

  return (
    <Container>
      {label && (
        <Text>
          {label}
          {rules?.required && <Text style={{ color: "red" }}> *</Text>}
        </Text>
      )}
      <Controller
        control={control}
        name={name}
        rules={rules}
        render={({ field: { onChange, value } }) => {
          const handleChangeText = (text: string) => {
            onChange(text);
          };

          const handleBlur = (event: any) => {
            if (value && typeof value === "string") {
              const trimmedValue = value.trimEnd();
              if (trimmedValue !== value) {
                onChange(trimmedValue);
              }
            }
            if (inputProps.onBlur) {
              inputProps.onBlur(event);
            }
          };

          return (
            <InputComponent
              value={value}
              onChangeText={handleChangeText}
              onBlur={handleBlur}
              error={!!error}
              {...inputProps}
            />
          );
        }}
      />
      {error && <Text variant="error">{error.message as string}</Text>}
    </Container>
  );
};

export default FormField;
