import {
  View,
  Text,
  Pressable,
  TextInput,
  ScrollView,
  Modal,
  Dimensions,
} from "react-native";
import { Image } from "expo-image";
import { styled } from "../utils/styled";
import { ComplaintStatusEnum } from "@/types/complaint";
export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled(View)`
  flex: 1;
  padding: 16px;
`;

export const Header = styled(View)`
  padding: 16px;
`;

export const SortInfoRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-horizontal: 16px;
`;

export const ItemsCountText = styled(Text)`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.text};
`;

export const SortButtonRow = styled(Pressable)`
  flex-direction: row;
  align-items: center;
`;

export const SortLabel = styled(Text)`
  font-size: 15px;
  color: ${({ theme }) => theme.colors.text};
`;

export const SortValueText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
  margin-right: 4px;
`;

export const ActiveFiltersContainer = styled(View)`
  padding-horizontal: 16px;
  margin-bottom: 12px;
`;

export const FilterChipsRow = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

export const ActiveFilterChip = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 6px 12px;
  border-radius: 16px;
  gap: 4px;
`;

export const ActiveFilterText = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.white};
  font-weight: 500;
`;
export const ManualEntityCard = styled(View)`
  padding: 16px;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  margin-bottom: 16px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.primary};
`;

export const SectionTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 12px;
  margin-top: 24px;
`;

export const SectionCard = styled(View)`
  background-color: ${({ theme }) => theme.colors.cardbackground};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
`;

export const InfoRow = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

export const InfoLabel = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  flex: 1;
`;

export const InfoValue = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  flex: 2;
  text-align: right;
`;

export const StatusContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
`;

export const HistoryItem = styled(View)`
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  margin-bottom: 8px;
`;

export const HistoryText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
`;

export const HistoryDate = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.gray};
  margin-top: 4px;
`;

export const Card = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.cardbackground};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  elevation: 2;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 4px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
`;

export const Title = styled(Text)`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const Label = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const Value = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const StatusBadge = styled(View)<{ status: number }>`
  padding: 6px 12px;
  margin-left: 5px;
  border-radius: 16px;
  background-color: ${(props) => {
    switch (props.status) {
      case 1:
        return props.theme.colors.statusPendingBg;
      case 2:
        return props.theme.colors.statusInReviewBg;
      case 3:
        return props.theme.colors.statusEstimationBg;
      case 4:
        return props.theme.colors.statusApprovedBg;
      case 5:
        return props.theme.colors.statusInProgressBg;
      case 6:
        return props.theme.colors.statusResolvedBg;
      case 7:
        return props.theme.colors.statusRejectedBg;
      default:
        return props.theme.colors.lightGray;
    }
  }};
`;

export const StatusText = styled(Text)<{ status: number }>`
  font-size: 14px;
  color: ${(props) => {
    switch (props.status) {
      case 1:
        return props.theme.colors.statusPending;
      case 2:
        return props.theme.colors.statusInReview;
      case 3:
        return props.theme.colors.statusEstimation;
      case 4:
        return props.theme.colors.statusApproved;
      case 5:
        return props.theme.colors.statusInProgress;
      case 6:
        return props.theme.colors.statusResolved;
      case 7:
        return props.theme.colors.statusRejected;
      default:
        return props.theme.colors.gray;
    }
  }};
`;

export const Input = styled(TextInput)`
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const SubmitButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  margin-top: 16px;
`;

export const SubmitButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const ErrorText = styled(Text)`
  color: ${({ theme }) => theme.colors.error};
  font-size: 14px;
  margin-top: 4px;
  margin-bottom: 16px;
`;

export const LoadingContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const LoadingText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  margin-top: 16px;
`;

export const EmptyContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

export const EmptyText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  text-align: center;
  margin-top: 16px;
`;

export const ActionButtonContainer = styled(View)`
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  flex: 1;
  /* height: 100px; */
`;

export const ActionButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  border-color: ${({ theme }) => theme.colors.primary};
  border-width: 1px;
  border-radius: 8px;
  padding: 12px;
  align-items: center;
`;

export const ActionButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  font-weight: 600;
`;

export const FloatingActionButton = styled(Pressable)`
  position: absolute;
  right: 16px;
  bottom: 16px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: ${({ theme }) => theme.colors.primary};
  justify-content: center;
  align-items: center;
  elevation: 4;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 4px;
`;

// New Complaint Form Styles
export const FormContainer = styled(ScrollView)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const FormContent = styled(View)``;

export const FormInputGroup = styled(View)`
  margin-bottom: 20px;
`;

export const FormLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
`;

export const FormInput = styled(TextInput)`
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text};
`;

export const FormTextArea = styled(FormInput)`
  height: 120px;
  padding-top: 12px;
`;

export const ImageUploadButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-style: dashed;
  height: 200px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
`;

export const UploadPlaceholder = styled(View)`
  align-items: center;
`;

export const UploadText = styled(Text)`
  margin-top: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const UploadedImage = styled(Image)`
  width: 100%;
  height: 100%;
  content-fit: cover;
`;

export const CloseButton = styled.Pressable`
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: ${({ theme }) => theme.colors.overlay};
  border-radius: 12px;
  padding: 4px;
`;

export const FormSubmitButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  margin-top: 20px;
`;

export const FormSubmitButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const ImageContainer = styled(Pressable)`
  margin: 10px 0;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

export const ImageScroll = styled.ScrollView`
  flex-direction: row;
`;

export const ComplaintImage = styled(Image)`
  width: 100px;
  height: 100px;
  border-radius: 8px;
  margin-right: 8px;
`;

export const FullScreenModal = styled(Modal)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.black};
`;

export const FullScreenImageContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.white};
`;

export const FullScreenImage = styled(Image)`
  width: ${Dimensions.get("window").width}px;
  height: ${Dimensions.get("window").height}px;
`;

// New Complaint Form Step Styles
export const StepContainer = styled(View)`
  margin-bottom: 16px;
`;

export const StepHeader = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  gap: 10px;
`;

export const StepTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const StepSubtitle = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-top: 4px;
`;

export const SelectionCard = styled(Pressable)<{ isSelected?: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-radius: 12px;
  border-width: 2px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.border};
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primaryLight : theme.colors.card};
  margin-bottom: 12px;
`;

export const SelectionIcon = styled(View)`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  justify-content: center;
  align-items: center;
  margin-right: 12px;
`;

export const SelectionContent = styled(View)`
  flex: 1;
`;

export const SelectionTitle = styled(Text)<{ isSelected?: boolean }>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.text};
  margin-bottom: 2px;
`;

export const SelectionSubtitle = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const EntityCard = styled(View)`
  padding: 16px;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  margin-bottom: 16px;
`;

export const EntityHeader = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

export const EntityIcon = styled(View)`
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  justify-content: center;
  align-items: center;
  margin-right: 8px;
`;

export const EntityName = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const EntityDetails = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const SearchButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-radius: 12px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.card};
  margin-bottom: 12px;
`;

export const SearchButtonText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const AddManuallyButton = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.lightGray};
`;

export const AddManuallyText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.primary};
  margin-left: 8px;
`;

export const ImagePreviewContainer = styled(View)`
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
`;

export const StepperContainer = styled(View)`
  padding: 16px 20px;
`;

export const StepContent = styled(View)`
  padding: 18px 24px;
`;

export const NavigationButtons = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.divider};
  gap: 10px;
`;

export const NavButton = styled(Pressable)<{
  variant?: "primary" | "secondary";
  disabled?: boolean;
}>`
  padding: 12px 24px;
  border-radius: 8px;
  background-color: ${({ theme, variant, disabled }) =>
    disabled
      ? theme.colors.lightGray
      : variant === "primary"
      ? theme.colors.primary
      : theme.colors.lightGray};
  align-items: center;
  justify-content: center;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const NavButtonText = styled(Text)<{
  variant?: "primary" | "secondary";
  disabled?: boolean;
}>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, variant, disabled }) =>
    disabled
      ? theme.colors.gray
      : variant === "primary"
      ? theme.colors.white
      : theme.colors.text};
`;

export const QRCodeSection = styled(View)`
  margin-bottom: 16px;
`;

export const QRCodeButton = styled(Pressable)<{ disabled?: boolean }>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-radius: 12px;
  border-width: 2px;
  border-style: dashed;
  border-color: ${({ theme, disabled }) =>
    disabled ? theme.colors.gray : theme.colors.primary};
  background-color: ${({ theme, disabled }) =>
    disabled ? theme.colors.lightGray : theme.colors.primaryLight};
  margin-bottom: 12px;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const QRCodeButtonText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.primary};
  margin-left: 8px;
  font-weight: 600;
`;

export const InlineFormContainer = styled.View`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  margin-top: 16px;
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.border};
`;

export const InlineFormHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export const InlineFormTitle = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const CloseInlineButton = styled.Pressable`
  padding: 4px;
`;

export const InlineFormContent = styled.View`
  position: relative;
`;

export const PhoneInputRow = styled.View`
  flex-direction: row;
  align-items: center;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
`;

export const CountryCodeButton = styled.Pressable`
  flex-direction: row;
  align-items: center;
  padding: 16px 12px;
  background-color: ${({ theme }) => theme.colors.card};
  border-right-width: 1px;
  border-right-color: ${({ theme }) => theme.colors.border};
`;

export const CountryCodeText = styled.Text`
  color: ${({ theme }) => theme.colors.text};
  font-size: 16px;
  font-weight: 600;
  margin-right: 4px;
`;

export const PhoneInputField = styled.TextInput`
  flex: 1;
  padding: 16px 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  background-color: ${({ theme }) => theme.colors.background};
`;

export const DropdownContainer = styled.View`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  margin-top: 4px;
  z-index: 1000;
  elevation: 5;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 3.84px;
`;

export const SearchContainer = styled.View`
  padding: 12px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const SearchInput = styled.TextInput`
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
  background-color: ${({ theme }) => theme.colors.background};
`;

export const DropdownList = styled.View`
  max-height: 200px;
`;

export const DropdownItem = styled.Pressable`
  padding: 12px 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const DropdownItemContent = styled.View`
  flex-direction: row;
  align-items: center;
`;

export const DropdownItemFlag = styled.View`
  margin-right: 12px;
  justify-content: center;
  align-items: center;
`;

export const DropdownItemText = styled.Text`
  flex: 1;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
`;

export const DropdownItemCode = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  font-weight: 500;
`;

export const InlineFormActions = styled.View`
  flex-direction: row;
  gap: 12px;
  margin-top: 16px;
`;

export const CancelInlineButton = styled.Pressable`
  flex: 1;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  align-items: center;
  justify-content: center;
`;

export const AddInlineButton = styled.Pressable<{ disabled: boolean }>`
  flex: 1;
  padding: 12px;
  background-color: ${({ theme, disabled }) =>
    disabled ? theme.colors.gray : theme.colors.primary};
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const LocationStatusContainer = styled(View)`
  padding: 8px;
  background-color: ${({ theme }) => theme.colors.statusInProgressBg};
  margin-bottom: 8px;
  border-radius: 4px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const LocationStatusText = styled(Text)`
  color: ${({ theme }) => theme.colors.statusInProgress};
  font-size: 14px;
`;

export const LocationErrorContainer = styled(View)`
  padding: 8px;
  background-color: ${({ theme }) => theme.colors.statusRejectedBg};
  margin-bottom: 8px;
  border-radius: 4px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const LocationErrorText = styled(Text)`
  color: ${({ theme }) => theme.colors.statusRejected};
  font-size: 14px;
  flex: 1;
`;

export const RetryButton = styled(Pressable)`
  padding: 4px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 4px;
`;

export const RetryButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 12px;
`;

export const LocationSuccessContainer = styled(View)`
  padding: 8px;
  background-color: ${({ theme }) => theme.colors.statusResolvedBg};
  margin-bottom: 8px;
  border-radius: 4px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const LocationSuccessText = styled(Text)`
  color: ${({ theme }) => theme.colors.statusResolved};
  font-size: 14px;
`;

// Bottom Sheet Styles
export const BottomSheetContainer = styled(View)`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding-bottom: 34px;
  elevation: 8;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px -2px;
  shadow-opacity: 0.25;
  shadow-radius: 8px;
`;

export const BottomSheetHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.border};
`;

export const BottomSheetTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
`;

export const BottomSheetCloseButton = styled(Pressable)`
  padding: 4px;
`;

export const BottomSheetContent = styled(View)`
  max-height: 400px;
  padding: 0 20px;
`;

export const BottomSheetActions = styled(View)`
  padding: 16px 20px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
`;

export const ConfirmButton = styled(Pressable)<{ disabled: boolean }>`
  background-color: ${({ theme, disabled }) =>
    disabled ? theme.colors.gray : theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  justify-content: center;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`;

export const ConfirmButtonText = styled(Text)<{ disabled: boolean }>`
  color: ${({ theme, disabled }) =>
    disabled ? theme.colors.text : theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

// ComplaintStep2 Styles
export const IssueSelectionCard = styled(Pressable)<{ isSelected: boolean }>`
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary + "10" : theme.colors.cardbackground};
  border-width: 2px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.border};
  border-radius: 12px;
  padding: 16px;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

export const IssueIconContainer = styled(View)<{ isSelected: boolean }>`
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.lightGray};
  justify-content: center;
  align-items: center;
  margin-right: 16px;
`;

export const IssueContentContainer = styled(View)`
  flex: 1;
`;

export const IssueTitleText = styled(Text)<{ isSelected: boolean }>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.text};
  margin-bottom: 4px;
`;

export const IssueDescriptionText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  line-height: 20px;
`;

export const ChevronIcon = styled(Pressable)`
  justify-content: center;
  align-items: center;
  padding: 4px;
`;

export const RequiredText = styled(Text)`
  color: ${({ theme }) => theme.colors.error};
  font-size: 14px;
  font-weight: 600;
`;

// Complaint Card Photo Styles
export const ComplaintCardImage = styled(ComplaintImage)`
  width: 60px;
  height: 60px;
  margin-right: 8px;
  border-radius: 4px;
`;

export const MorePhotosText = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.gray};
  align-self: center;
  margin-left: 8px;
`;

export const TapToChangeText = styled(Text)`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  margin-top: 8px;
  font-style: italic;
`;

export const ImagePreviewRow = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
`;

export const ImagePreviewItem = styled(ImageUploadButton)`
  width: 80px;
  height: 80px;
  position: relative;
`;

export const ImagePreviewImage = styled(UploadedImage)`
  width: 100%;
  height: 100%;
`;

export const ImagePreviewCloseButton = styled(CloseButton)`
  position: absolute;
  top: 4px;
  right: 4px;
`;

export const UploadButtonDisabled = styled(ImageUploadButton)<{
  isValidating: boolean;
}>`
  opacity: ${({ isValidating }) => (isValidating ? 0.6 : 1)};
`;

export const FileInfoText = styled(UploadText)`
  font-size: 12px;
  margin-top: 8px;
  opacity: 0.7;
`;

export const FileInfoTextNoMargin = styled(UploadText)`
  font-size: 12px;
  opacity: 0.7;
`;

export const QRCodeButtonContent = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-vertical: 28px;
`;

export const QRCodeButtonTextContainer = styled(View)`
  margin-left: 16px;
`;

export const QRCodeButtonSubtitle = styled(Text)`
  color: ${({ theme }) => theme.colors.gray};
  font-size: 13px;
  margin-top: 4px;
`;

export const QRCodeButtonTitle = styled(QRCodeButtonText)`
  font-size: 18px;
`;
