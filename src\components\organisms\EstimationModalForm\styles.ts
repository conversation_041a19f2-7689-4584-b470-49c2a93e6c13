import { styled } from "@/utils/styled";
import { View, Text, Pressable } from "react-native";

export const Form = styled(View)`
  padding: 20px;
`;

export const DateTimeFieldContainer = styled(View)`
  margin-bottom: 16px;
`;

export const DateTimeFieldLabel = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const DateTimeFieldTouchable = styled(Pressable)<{ $loading?: boolean }>`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.gray};
  border-radius: 8px;
  padding: 12px;
  background-color: ${({ theme, $loading }) =>
    $loading ? theme.colors.disabled : theme.colors.background};
`;

export const DateTimeFieldText = styled(Text)<{ $hasValue?: boolean }>`
  color: ${({ theme, $hasValue }) =>
    $hasValue ? theme.colors.text : theme.colors.gray};
`;

export const PickerFieldContainer = styled(View)`
  margin-bottom: 16px;
`;

export const PickerFieldLabel = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const PickerFieldWrapper = styled(View)`
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.gray};
  border-radius: 8px;
  overflow: hidden;
`;

// Quantity Field Styles
export const QuantityFieldContainer = styled(View)`
  margin-bottom: 16px;
`;

export const QuantityFieldLabel = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const QuantityButton = styled(Pressable)<{ $disabled?: boolean }>`
  padding: 8px;
  background-color: ${({ theme }) => theme.colors.gray};
  border-radius: 4px;
  margin-left: 0px;
  margin-right: 0px;
  opacity: ${({ $disabled }) => ($disabled ? 0.5 : 1)};
`;

export const QuantityValueText = styled(Text)<{ $isValue?: boolean }>`
  min-width: ${({ $isValue }) => ($isValue ? "32px" : "auto")};
  text-align: center;
  font-size: 16px;
  color: ${({ theme, $isValue }) =>
    $isValue ? theme.colors.text : theme.colors.background};
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  border-top-width: 1px;
border-top-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  gap: 12px;
  background-color: ${({ theme }) => theme.colors.card};
`;

export const CancelButton = styled(Pressable)`
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  background-color: transparent;
  align-items: center;
  justify-content: center;
`;

export const ConfirmButton = styled(Pressable)`
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
  align-items: center;
  justify-content: center;
`;

export const ButtonText = styled(Text)<{ variant?: "cancel" | "confirm" }>`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme, variant }) =>
    variant === "cancel" ? theme.colors.text : theme.colors.white};
`;