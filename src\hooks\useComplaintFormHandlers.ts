import { useTranslation } from "react-i18next";
import Toast from "react-native-toast-message";
import { AppDispatch } from "@/store/store";
import { VendorDetails } from "@/types/vendor";
import { Customer } from "@/types/customer";
import { CountryCode } from "@/types/auth";
import { getComplaintByQRCodeAction } from "@/store/actions/complaint";

interface FormHandlersParams {
  complaintFor: "vendor" | "customer";
  setValue: (field: string, value: any) => void;
  setShowAddManuallyForm: (show: boolean) => void;
  dispatch: AppDispatch;
}

export const useComplaintFormHandlers = ({
  complaintFor,
  setValue,
  setShowAddManuallyForm,
  dispatch,
}: FormHandlersParams) => {
  const { t } = useTranslation();

  const handleEntitySelect = (entity: VendorDetails | Customer) => {
    if (complaintFor === "vendor") {
      const vendor = entity as VendorDetails;
      setValue("selectedVendor", vendor);
      setValue("vendorMobileNumber", "");
      setValue("vendorCountryCode", undefined);
      setValue("vendorFirstName", "");
      setValue("vendorLastName", "");
    } else {
      const customer = entity as Customer;
      setValue("selectedCustomer", customer);
      setValue("customerMobileNumber", "");
      setValue("customerCountryCode", undefined);
      setValue("customerFirstName", "");
      setValue("customerLastName", "");
    }
  };

  const handleAddManuallyForm = (
    mobileNumber: string,
    countryCode: CountryCode,
    firstName: string,
    lastName: string
  ) => {
    if (complaintFor === "vendor") {
      setValue("vendorMobileNumber", mobileNumber);
      setValue("vendorCountryCode", countryCode);
      setValue("vendorFirstName", firstName);
      setValue("vendorLastName", lastName);
      setValue("selectedVendor", undefined);
    } else {
      setValue("customerMobileNumber", mobileNumber);
      setValue("customerCountryCode", countryCode);
      setValue("customerFirstName", firstName);
      setValue("customerLastName", lastName);
      setValue("selectedCustomer", undefined);
    }
    setShowAddManuallyForm(false);
  };

  const handleComplaintTypeChange = (type: "vendor" | "customer") => {
    setValue("complaintFor", type);
  };

  const handleBatteryBrandChange = (brand: "ozone" | "other") => {
    setValue("batteryBrand", brand);
    if (brand === "ozone") {
      setValue("productImages", []);
      setValue("brandName", "");
    } else {
      setValue("serialNumber", "");
      setValue("qrCode", "");
    }
  };

  const handleQRCodeChange = async (value: string) => {
    try {
      const response = await dispatch(
        getComplaintByQRCodeAction({ slug: value })
      ).unwrap();
      setValue("serialNumber", response.data?.serial_no);
      setValue("qrCode", value);
    } catch (error: any) {
      console.error("Error fetching QR code details:", error);
      Toast.show({
        type: "error",
        text1: error.message,
      });
    }
  };

  return {
    handleEntitySelect,
    handleAddManuallyForm,
    handleComplaintTypeChange,
    handleBatteryBrandChange,
    handleQRCodeChange,
  };
};
