import React from "react";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "@/hooks/useTheme";
import { Container, LogoContainer, Logo } from "./CustomSplashScreen.styles";

const LOGO = require("../../../assets/icon.png");

const CustomSplashScreen: React.FC = () => {
  const { theme } = useTheme();
  const gradientColors: [string, string] = [
    theme.colors.background,
    theme.colors.primaryLight,
  ];

  return (
    <LinearGradient colors={gradientColors} style={{ flex: 1 }}>
      <Container>
        <LogoContainer>
          <Logo source={LOGO} resizeMode="contain" />
        </LogoContainer>
      </Container>
    </LinearGradient>
  );
};

export default CustomSplashScreen;
