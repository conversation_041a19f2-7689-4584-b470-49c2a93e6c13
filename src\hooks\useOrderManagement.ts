import { useState, useCallback } from "react";
import { useRouter } from "expo-router";
import { useCart } from "@/hooks/useCart";
import Toast from "react-native-toast-message";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";

export const useOrderManagement = () => {
  const router = useRouter();
  const { placeOrder } = useCart();

  // Order state
  const [isPlacingOrder, setIsPlacingOrder] = useState<boolean>(false);

  const handlePlaceOrder = useCallback(
    async (
      vendorId: number | null,
      billingAddressId: number | null,
      shippingAddressId: number | null,
      paymentMethod: PaymentMethod,
      paymentProof: ImagePicker.ImagePickerAsset | null,
      selectedPaymentDetails,
      isReadyForCheckout: () => boolean
    ) => {
      if (!isReadyForCheckout()) {
        Toast.show({
          type: "error",
          text1: "Please select addresses to continue",
        });
        return;
      }
      try {
        setIsPlacingOrder(true);
        await placeOrder(
          vendorId,
          billingAddressId,
          shippingAddressId,
          paymentMethod,
          paymentProof,
          selectedPaymentDetails
        );
      } catch (error: any) {
        Toast.show({
          type: "error",
          text1: error?.message || "Unknown error occurred",
        });
      } finally {
        setIsPlacingOrder(false);
      }
    },
    [placeOrder, router]
  );

  return {
    // State
    isPlacingOrder,

    // Actions
    handlePlaceOrder,
  };
};
