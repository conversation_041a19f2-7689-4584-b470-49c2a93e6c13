import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/services/api";
import {
  GetCustomerListParams,
  GetCustomerListResponse,
} from "@/types/customer";
import { withToastForError } from "@/utils/thunk";

export const getCustomerListAction = createAsyncThunk(
  "customer/getCustomerList",
  withToastForError(
    async (params: GetCustomerListParams): Promise<GetCustomerListResponse> => {
      const response = await api.get("/customer-list", { params });
      return {
        data: response.data.data,
        meta: response.data.pagination,
        status: response.data.status,
        message: "Success",
      };
    }
  )
);
