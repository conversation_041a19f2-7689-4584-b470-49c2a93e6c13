import React from "react";
import { Modal } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useLocation } from "@/hooks/useLocation";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  Overlay,
  Container,
  IconContainer,
  Title,
  Description,
  ButtonContainer,
  Button,
  ButtonText,
  FooterText,
} from "./LocationPermissionModal.styles";

interface LocationPermissionModalProps {
  isVisible: boolean;
  onComplete: () => void;
}

const LocationPermissionModal: React.FC<LocationPermissionModalProps> = ({
  isVisible,
  onComplete,
}) => {
  const { handlePermissionRequest, isLoading } = useLocation();
  const { theme } = useTheme();
  const { t } = useTranslation();

  const handleAllowLocation = async () => {
    await handlePermissionRequest();
    onComplete();
  };

  const handleSkip = () => {
    onComplete();
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <Overlay>
        <Container>
          <IconContainer>
            <Ionicons name="location" size={60} color={theme.colors.primary} />
          </IconContainer>

          <Title>
            {t("location_permission.title", "Enable Location Access")}
          </Title>

          <Description>
            {t(
              "location_permission.description",
              "Allow Ozone Batteries to access your location to improve your overall experience."
            )}
          </Description>

          <ButtonContainer>
            <Button
              variant="primary"
              onPress={handleAllowLocation}
              disabled={isLoading}
            >
              <ButtonText variant="primary">
                {isLoading
                  ? t("location_permission.requesting", "Requesting...")
                  : t("location_permission.allow", "Allow Location Access")}
              </ButtonText>
            </Button>

            <Button
              variant="secondary"
              onPress={handleSkip}
              disabled={isLoading}
            >
              <ButtonText variant="secondary">
                {t("location_permission.skip", "Skip for Now")}
              </ButtonText>
            </Button>
          </ButtonContainer>

          <FooterText>
            {t(
              "location_permission.footer",
              "You can change this later in your device settings"
            )}
          </FooterText>
        </Container>
      </Overlay>
    </Modal>
  );
};

export default LocationPermissionModal;
