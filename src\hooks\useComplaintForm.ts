import { useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useAppSelector } from "@/store/store";
import { UserType } from "@/types/api";
import { useForm } from "react-hook-form";
import { VendorDetails } from "@/types/vendor";
import { Customer } from "@/types/customer";
import { CountryCode } from "@/types/auth";

interface ComplaintFormData {
  complaintFor: "vendor" | "customer";
  selectedVendor?: VendorDetails;
  selectedCustomer?: Customer;
  vendorMobileNumber: string;
  customerMobileNumber: string;
  vendorCountryCode?: CountryCode;
  customerCountryCode?: CountryCode;
  vendorFirstName: string;
  vendorLastName: string;
  customerFirstName: string;
  customerLastName: string;
  batteryBrand: "ozone" | "other";
  serialNumber: string;
  qrCode: string;
  productImages: string[];
  brandName: string;
  description: string;
  selectedIssue: string;
  customDescription: string;
  selectedBillingAddressId: number | null;
  selectedShippingAddressId: number | null;
  useBillingAsShipping: boolean;
}

export const useComplaintForm = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.auth);

  // Determine user type and flow
  const isSalesPerson = user?.role_id === UserType.SALESPERSON;
  const isVendor = user?.role_id === UserType.VENDOR;
  const isCustomer = user?.role_id === UserType.CUSTOMER;

  // For VENDOR and CUSTOMER, skip step 1 and start at step 2
  const shouldSkipStep1 = isVendor || isCustomer;
  const initialStep = shouldSkipStep1 ? 2 : 1;

  // Set default complaint type based on user role
  const getDefaultComplaintFor = () => {
    if (isVendor) return "customer";
    if (isCustomer) return "vendor";
    return "vendor"; // Default for SALESPERSON
  };

  // Step data for Stepper - adapt based on user type
  const stepData = useMemo(() => {
    if (shouldSkipStep1) {
      return [
        { id: 2, label: t("complaint.steps.product_details") },
        { id: 3, label: t("complaint.steps.address") },
      ];
    }
    return [
      { id: 1, label: t("complaint.steps.complaint_details") },
      { id: 2, label: t("complaint.steps.product_details") },
      { id: 3, label: t("complaint.steps.address") },
    ];
  }, [shouldSkipStep1, t]);

  const methods = useForm<ComplaintFormData>({
    defaultValues: {
      complaintFor: getDefaultComplaintFor(),
      batteryBrand: "ozone",
      vendorMobileNumber: "",
      customerMobileNumber: "",
      vendorCountryCode: undefined,
      customerCountryCode: undefined,
      vendorFirstName: "",
      vendorLastName: "",
      customerFirstName: "",
      customerLastName: "",
      serialNumber: "",
      qrCode: "",
      productImages: [],
      brandName: "",
      description: "",
      selectedIssue: "",
      customDescription: "",
      selectedBillingAddressId: null,
      selectedShippingAddressId: null,
      useBillingAsShipping: true,
    },
  });

  const {
    watch,
    setValue,
    handleSubmit,
    formState: { isValid },
  } = methods;

  // Set initial complaint type when component mounts
  useEffect(() => {
    if (shouldSkipStep1) {
      setValue("complaintFor", getDefaultComplaintFor());
    }
  }, [shouldSkipStep1, setValue]);

  const complaintFor = watch("complaintFor");
  const batteryBrand = watch("batteryBrand");
  const selectedVendor = watch("selectedVendor");
  const selectedCustomer = watch("selectedCustomer");
  const selectedIssue = watch("selectedIssue");
  const customDescription = watch("description");
  const selectedBillingAddressId = watch("selectedBillingAddressId");
  const selectedShippingAddressId = watch("selectedShippingAddressId");
  const useBillingAsShipping = watch("useBillingAsShipping");

  const isStep1Valid = () => {
    if (!complaintFor) return false;

    // If entity is selected, that's valid
    if (selectedVendor || selectedCustomer) return true;

    // If no entity selected, check manual entry
    const mobileNumber =
      complaintFor === "vendor"
        ? watch("vendorMobileNumber")
        : watch("customerMobileNumber");
    const firstName =
      complaintFor === "vendor"
        ? watch("vendorFirstName")
        : watch("customerFirstName");
    const lastName =
      complaintFor === "vendor"
        ? watch("vendorLastName")
        : watch("customerLastName");

    return (
      mobileNumber &&
      mobileNumber.trim().length > 9 &&
      firstName &&
      firstName.trim().length > 0 &&
      lastName &&
      lastName.trim().length > 0
    );
  };

  const isStep2Valid = () => {
    const hasIssue = !!selectedIssue;
    const hasDescription = !!customDescription?.trim();
    if (batteryBrand === "ozone") {
      const hasSerialOrQR = !!(watch("serialNumber") || watch("qrCode"));
      const hasIssueOrDescription = hasIssue || hasDescription;

      return hasSerialOrQR && hasIssueOrDescription;
    } else {
      const hasProductInfo = !!(
        watch("productImages")?.length > 0 && watch("brandName")
      );
      const hasIssueOrDescription = hasIssue || hasDescription;

      return hasProductInfo && hasIssueOrDescription;
    }
  };

  const isStep3Valid = () => {
    if (!selectedBillingAddressId) return false;

    if (!useBillingAsShipping && !selectedShippingAddressId) return false;

    return true;
  };

  return {
    shouldSkipStep1,
    initialStep,
    stepData,
    methods,
    watch,
    setValue,
    handleSubmit,
    complaintFor,
    batteryBrand,
    selectedVendor,
    selectedCustomer,
    selectedIssue,
    customDescription,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    isStep1Valid,
    isStep2Valid,
    isStep3Valid,
  };
};
