import { styled } from "@/utils/styled";
import { View, Text, Pressable, TextInput } from "react-native";

export const StyledBottomSheetView = styled(View)`
  flex: 1;
  padding: 20px;
`;

export const Title = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const SearchContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.background};
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border || "#E9ECEF"};
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
`;

export const SearchInput = styled(TextInput)`
  flex: 1;
  margin-left: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const ItemsCountText = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 16px;
`;

export const ProductItem = styled(Pressable)`
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  background-color: ${({ theme }) => theme.colors.card};
  margin-bottom: 1px;
`;

export const ProductInfo = styled(View)`
  flex: 1;
`;

export const ProductName = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const ProductSku = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 4px;
`;

export const ProductPrice = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.primary};
`;

export const EmptyContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
`;

export const EmptyText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
`;
