import { createAsyncThunk } from "@reduxjs/toolkit";
import { ApiResponse } from "../../types/api";
import api from "../../services/api";
import { withToastForError } from "../../utils/thunk";

export interface AddAddressPayload {
  billing_shipping: number;
  contact_person_name?: string;
  company_name?: string;
  address_line_one: string;
  address_line_two?: string;
  gst_number?: string;
  post_code: string;
  country_id: number;
  state_id: number;
  city_id: number;
  phone?: string;
  address_id?: number;
  user_id?: number;
}

export interface AddressResponse extends ApiResponse {
  data: {
    id: number;
    billing_shipping: number;
    contact_person_name: string;
    company_name: string;
    address_line_one: string;
    address_line_two: string;
    gst_number: string;
    post_code: string;
    country_id: number;
    state_id: number;
    city_id: number;
    phone: string;
    created_at: string;
    updated_at: string;
  };
}

export const addUpdateAddressAction = createAsyncThunk(
  "address/addAddress",
  withToastForError(
    async (payload: AddAddressPayload): Promise<AddressResponse> => {
      const response = await api.post("/address-insert-update", payload);
      return response.data;
    }
  )
);

export const getAddressListAction = createAsyncThunk(
  "address/getAddressList",
  withToastForError(
    async (payload: {
      user_id?: number;
      page?: number;
    }): Promise<AddressResponse> => {
      const response = await api.get("/address-list", { params: payload });
      return response.data;
    }
  )
);

export interface UpdateAddressPayload extends AddAddressPayload {
  address_id: number;
}

export const deleteAddressAction = createAsyncThunk(
  "address/deleteAddress",
  withToastForError(
    async (payload: UpdateAddressPayload): Promise<AddressResponse> => {
      const response = await api.post("/address-delete", payload);
      return response.data;
    }
  )
);
