import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  CreateComplaintPayload,
  ComplaintListResponse,
  ComplaintListPayload,
  ComplaintStatus,
  ComplaintIssueListResponse,
  ComplaintStatusEnum,
  ServicePersonListResponse,
  ServicePersonListPayload,
  AssignComplaintPayload,
} from "../../types/complaint";
import { withToastForError } from "../../utils/thunk";
import { ApiResponse } from "../../types/api";
import api, { makeMultipartRequest } from "@/services/api";

export const getComplaintsListAction = createAsyncThunk(
  "complaint/getComplaintsList",
  withToastForError(
    async (payload: ComplaintListPayload): Promise<ComplaintListResponse> => {
      const response = await api.get("/complaint-list", { params: payload });
      return { ...response.data, meta: payload };
    }
  )
);

export const complaintInsertUpdateAction = createAsyncThunk(
  "complaint/insertUpdate",
  withToastForError(
    async (payload: CreateComplaintPayload): Promise<ApiResponse> => {
      const response = await makeMultipartRequest(
        "/complaint-insert-update",
        payload,
        payload.product_serial_photo && payload.product_serial_photo.length > 0
          ? {
              fieldName: "product_serial_photo",
              images: payload.product_serial_photo,
              fileNamePrefix: "complaint",
            }
          : undefined
      );
      return response.data;
    }
  )
);

export const getComplaintByQRCodeAction = createAsyncThunk(
  "complaint/getByQRCode",
  withToastForError(async (params: { slug: string }): Promise<ApiResponse> => {
    const response = await api.get(`/product-qr-code-details`, {
      params,
    });
    return response.data;
  })
);

export const getComplaintStatusAction = createAsyncThunk(
  "complaint/getStatus",
  withToastForError(async (): Promise<ComplaintStatus> => {
    const response = await api.get(`/complaint-status-list`);
    return response.data;
  })
);

export const getComplaintIssueListAction = createAsyncThunk(
  "complaint/getIssueList",
  withToastForError(async (): Promise<ComplaintIssueListResponse> => {
    const response = await api.get(`/complaint-issue-list`);
    return response.data;
  })
);

export const complaintStatusChangeAction = createAsyncThunk(
  "complaint/complaintStatusChange",
  withToastForError(
    async (payload: {
      complaint_id: string;
      status: ComplaintStatusEnum;
      description?: string;
    }): Promise<ApiResponse> => {
      const response = await api.post(`/complaint-status-change`, payload);
      return response.data;
    }
  )
);

export const getServicePersonListAction = createAsyncThunk(
  "complaint/getServicePersonList",
  withToastForError(
    async (
      payload: ServicePersonListPayload
    ): Promise<ServicePersonListResponse> => {
      const response = await api.get(`/service-person-list`, {
        params: payload,
      });
      return { ...response.data, meta: payload };
    }
  )
);

export const assignComplaintAction = createAsyncThunk(
  "complaint/assignComplaint",
  withToastForError(
    async (payload: AssignComplaintPayload): Promise<ApiResponse> => {
      const response = await api.post(`/assign-complaint`, payload);
      return response.data;
    }
  )
);

export const sendComplaintEstimationAction = createAsyncThunk(
  "complaint/sendComplaintEstimation",
  withToastForError(
    async (payload: {
      slug: string;
      estimate_time: string;
      products?: {
        slug: string;
        quantity: number;
      }[];
    }): Promise<ApiResponse> => {
      const response = await api.post(`/complaint-estimation`, payload);
      return response.data;
    }
  )
);

export const getPartListAction = createAsyncThunk(
  "complaint/getPartList",
  withToastForError(
    async (payload: {
      search?: string;
      page?: number;
    }): Promise<ApiResponse> => {
      const response = await api.get(`/part-list`, { params: payload });
      return response.data;
    }
  )
);
