import React, { useState } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { sendComplaintEstimationAction } from "@/store/actions/complaint";
import { Container, Content } from "@/styles/Estimation.styles";
import { Header } from "@/components";
import { Part } from "@/types/complaint";
import Toast from "react-native-toast-message";
import FormTemplate from "@/template/FormTemplate";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import EstimationForm from "@/components/organisms/EstimationForm";

interface EstimationFormData {
  estimate_time: string;
  products?: Array<{
    part?: Part;
    slug: string;
    quantity: number;
  }>;
}

const validationSchema = yup.object().shape({
  estimate_time: yup.string().required("Estimate time is required"),
  products: yup
    .array()
    .of(
      yup.object().shape({
        part: yup.object().required(),
        slug: yup.string().required(),
        quantity: yup.number().min(1).required(),
      })
    )
    .optional(),
});

const EstimationFormScreen = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { slug } = useLocalSearchParams<{ slug: string }>();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: EstimationFormData) => {
    if (!slug) {
      Toast.show({
        type: "error",
        text1: t("common.error") || "Error",
      });
      return;
    }

    setLoading(true);
    try {
      // Transform products for API
      // Format estimate_time as 'YYYY-MM-DD HH:mm:ss'

      const payload = {
        slug,
        estimate_time: data.estimate_time,
        products: data.products.map(({ slug, quantity }) => ({
          slug,
          quantity,
        })),
      };
      console.log({ payload });

      const res = await dispatch(
        sendComplaintEstimationAction(payload)
      ).unwrap();

      Toast.show({
        type: "success",
        text1: res.message,
      });
      router.back();
    } catch (error: any) {
      console.error("Estimation submission error:", error);
      Toast.show({
        type: "error",
        text1: t("common.error") || "Error",
        text2: error?.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const defaultValues = {
    slug,
    estimate_time: "",
    products: [],
  };

  return (
    <Container>
      <Header
        title={t("estimation.send_estimation") || "Send Estimation"}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <Content>
        <FormTemplate<EstimationFormData>
          Component={(props) => (
            <EstimationForm {...props} loading={loading} t={t} />
          )}
          defaultValues={defaultValues}
          resolver={yupResolver(validationSchema)}
          mode="onChange"
          onSubmit={handleSubmit}
        />
      </Content>
    </Container>
  );
};

export default EstimationFormScreen;
