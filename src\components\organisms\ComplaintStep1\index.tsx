import React from "react";
import { useTranslation } from "react-i18next";
import { VendorDetails } from "@/types/vendor";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  StepHeader,
  StepTitle,
  StepSubtitle,
} from "@/styles/Complaint.styles";
import { ComplaintTypeSelector } from "@/components/molecules/ComplaintTypeSelector";
import { EntitySelector } from "@/components/molecules/EntitySelector";

interface ComplaintStep1Props {
  complaintFor: "vendor" | "customer";
  selectedVendor?: VendorDetails;
  selectedCustomer?: any;
  mobileNumber: string;
  onComplaintTypeChange: (type: "vendor" | "customer") => void;
  onAddManually: () => void;
  onSearchPress: () => void;
}

export const ComplaintStep1: React.FC<ComplaintStep1Props> = ({
  complaintFor,
  selectedVendor,
  selectedCustomer,
  mobileNumber,
  onComplaintTypeChange,
  onAddManually,
  onSearchPress,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <StepContainer>
        <StepHeader>
          <StepTitle>{t("complaint.form.choose_complaint_for")}</StepTitle>
        </StepHeader>

        <ComplaintTypeSelector
          selectedType={complaintFor}
          onTypeSelect={onComplaintTypeChange}
        />
      </StepContainer>

      {complaintFor && (
        <StepContainer>
          <StepHeader>
            <StepTitle>
              {complaintFor === "vendor"
                ? t("complaint.form.select_vendor")
                : t("complaint.form.select_customer")}
            </StepTitle>
            <StepSubtitle>
              {t("complaint.form.search_existing_add_manually")}
            </StepSubtitle>
          </StepHeader>

          <EntitySelector
            complaintFor={complaintFor}
            selectedVendor={selectedVendor}
            selectedCustomer={selectedCustomer}
            mobileNumber={mobileNumber}
            onSearchPress={onSearchPress}
            onAddManually={onAddManually}
          />
        </StepContainer>
      )}
    </>
  );
};
