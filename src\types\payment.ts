import * as ImagePicker from "expo-image-picker";
import { OfferCartList } from "@/types/cart";

export enum PaymentMethod {
  Account_Number = 1,
  UPI_Id = 2,
  QR_Code = 3,
  Credit = 4,
}

export interface PaymentMethodData {
  id: PaymentMethod;
  label: string;
  description?: string;
  icon: string;
  details: Record<string, any>;
}

export interface PaymentStepProps {
  onBack: () => void;
  onContinue: (
    selectedMethod: PaymentMethod,
    paymentProof?: ImagePicker.ImagePickerAsset,
    selectedPaymentDetails?: any
  ) => void;
  selectedPaymentMethod?: PaymentMethod | null;
  paymentProof?: ImagePicker.ImagePickerAsset | null;
  selectedPaymentDetails?: {
    account_number?: string | null;
    upi_id?: string | null;
    ifsc_code?: string | null;
    qr_code?: string | null;
  } | null;
  isPlacingOrder?: boolean;
}

// Payment methods that require proof
export const PAYMENT_METHODS_REQUIRING_PROOF = [
  PaymentMethod.Account_Number,
  PaymentMethod.UPI_Id,
  PaymentMethod.QR_Code,
];
