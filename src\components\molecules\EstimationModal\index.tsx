import React from "react";
import { Modal, KeyboardAvoidingView, Platform } from "react-native";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  <PERSON>dalOverlay,
  ModalContent,
  <PERSON>dalHeader,
  ModalTitle,
  CloseButton,
} from "./styles";
import FormTemplate from "@/template/FormTemplate";
import { EstimationForm } from "@/components/organisms/EstimationModalForm";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Ionicons } from "@expo/vector-icons";

interface EstimationData {
  product_id: string;
  quantity: number;
  estimate_time: Date;
}

interface EstimationModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (data: EstimationData) => Promise<void>;
  loading?: boolean;
}

const schema = yup.object().shape({
  product_id: yup.string().required("Product is required"),
  estimate_time: yup.date().required("Estimate time is required"),
});

const EstimationModal: React.FC<EstimationModalProps> = ({
  visible,
  onClose,
  onConfirm,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const handleFormSubmit = async (data: EstimationData) => {
    try {
      await onConfirm(data);
    } catch (error) {
      throw error;
    }
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                {t("estimation.send_estimation_details") ||
                  "Send Estimation Details"}
              </ModalTitle>
              <CloseButton onPress={onClose} disabled={loading}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </CloseButton>
            </ModalHeader>

            <FormTemplate<EstimationData>
              Component={(props) => (
                <>
                  <EstimationForm
                    {...props}
                    loading={loading}
                    t={t}
                    theme={theme}
                    hideButtons
                  />
                </>
              )}
              defaultValues={{
                product_id: "",
                quantity: 0,
                estimate_time: new Date(),
              }}
              resolver={yupResolver(schema)}
              mode="onChange"
              onSubmit={handleFormSubmit}
              onCancel={onClose}
            />
          </ModalContent>
        </ModalOverlay>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default EstimationModal;