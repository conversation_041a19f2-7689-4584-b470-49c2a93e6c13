import { styled } from "@/utils/styled";
import { SectionList } from "react-native";

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${({ theme }) => theme.colors.primary};
`;

export const SearchContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 16px;
`;

export const SearchInput = styled.TextInput`
  flex: 1;
  margin-left: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const ItemsCountText = styled.Text`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.text};
  font-weight: 500;
  margin-bottom: 16px;
`;

export const ListContainer = styled(SectionList)`
  flex: 1;
`;

export const PickerItem = styled.Pressable`
  padding: 16px 0px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const PickerItemText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const EmptyContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 200px;
`;

export const EmptyText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-align: center;
`;

export const LoadingContainer = styled.View`
  padding: 20px;
  align-items: center;
  justify-content: center;
`;

export const SectionHeaderContainer = styled.View`
  padding: 20px 20px 0px 20px;
`;

export const StyledBottomSheetView = styled.View`
  padding: 20px;
  flex: 1;
`;
