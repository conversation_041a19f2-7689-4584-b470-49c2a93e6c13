import React, { useCallback } from "react";
import OrderSummary from "@/components/organisms/OrderSummary";
import CartFooter from "@/components/organisms/CartFooter";
import { useCartManagement } from "@/hooks/useCartManagement";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { useOrderManagement } from "@/hooks/useOrderManagement";
import { VendorDetails } from "@/types/vendor";

interface SummaryStepProps {
  onBack: () => void;
  onContinue: () => void;
  selectedBillingAddressId?: number | null;
  selectedShippingAddressId?: number | null;
  useBillingAsShipping?: boolean;
  selectedVendor?: VendorDetails | null;
  couponCode: string;
  setCouponCode: (val: string) => void;
  offerCartList: any;
  setOfferCartList: (val: any) => void;
  isCheckingCoupon: boolean;
  setIsCheckingCoupon: (val: boolean) => void;
  couponError: string | null;
  setCouponError: (val: string | null) => void;
  handleCheckCoupon: () => void;
  hasValidCoupon: boolean;
}

const SummaryStep: React.FC<SummaryStepProps> = ({
  onBack,
  onContinue,
  selectedBillingAddressId: externalBillingId,
  selectedShippingAddressId: externalShippingId,
  useBillingAsShipping: externalUseBillingAsShipping,
  selectedVendor,
  couponCode,
  setCouponCode,
  offerCartList,
  setOfferCartList,
  isCheckingCoupon,
  setIsCheckingCoupon,
  couponError,
  setCouponError,
  handleCheckCoupon,
  hasValidCoupon,
}) => {
  const { cartList } = useCartManagement();
  const addressManagement = useAddressManagement();

  const selectedBillingAddressId =
    externalBillingId !== undefined
      ? externalBillingId
      : addressManagement.selectedBillingAddressId;
  const selectedShippingAddressId =
    externalShippingId !== undefined
      ? externalShippingId
      : addressManagement.selectedShippingAddressId;
  const useBillingAsShipping =
    externalUseBillingAsShipping !== undefined
      ? externalUseBillingAsShipping
      : addressManagement.useBillingAsShipping;

  const getEffectiveShippingAddressId = () => {
    return useBillingAsShipping
      ? selectedBillingAddressId
      : selectedShippingAddressId;
  };

  // Get selected addresses based on current state
  const getSelectedBillingAddress = () => {
    const addresses = addressManagement.getAddresses();
    return (
      addresses.billing.find(
        (addr: any) => addr.id === selectedBillingAddressId
      ) || null
    );
  };

  const getSelectedShippingAddress = () => {
    const addresses = addressManagement.getAddresses();
    const effectiveShippingId = getEffectiveShippingAddressId();
    return (
      addresses.all.find((addr: any) => addr.id === effectiveShippingId) || null
    );
  };

  const { isReadyForCheckout } = addressManagement;

  // Convert cartList to CartSummaryData format
  const cartSummaryData = cartList
    ? {
        sub_total: cartList.sub_total || "0",
        cgst_total: cartList.cgst_total || "0",
        cgst_percentage: cartList.cgst_percentage || "0",
        sgst_total: cartList.sgst_total || "0",
        sgst_percentage: cartList.sgst_percentage || "0",
        igst_total: cartList.igst_total || "0",
        igst_percentage: cartList.igst_percentage || "0",
        total_amount: cartList.total_amount || "0",
        cartItems: (cartList.cartItems as any) || [],
      }
    : null;

  const handleContinueToPayment = useCallback(() => {
    onContinue();
  }, [onContinue]);
  return (
    <>
      <OrderSummary
        cartData={cartSummaryData}
        billingAddress={getSelectedBillingAddress()}
        shippingAddress={getSelectedShippingAddress()}
        shipToSameAddress={useBillingAsShipping}
        selectedVendor={selectedVendor}
        couponCode={couponCode}
        setCouponCode={setCouponCode}
        offerCartList={offerCartList}
        setOfferCartList={setOfferCartList}
        isCheckingCoupon={isCheckingCoupon}
        setIsCheckingCoupon={setIsCheckingCoupon}
        couponError={couponError}
        setCouponError={setCouponError}
        handleCheckCoupon={handleCheckCoupon}
        hasValidCoupon={hasValidCoupon}
      />

      <CartFooter
        onClearCart={onBack}
        onContinue={handleContinueToPayment}
        clearButtonTitle="Back"
        continueButtonTitle="Continue to Payment"
      />
    </>
  );
};

export default SummaryStep;
