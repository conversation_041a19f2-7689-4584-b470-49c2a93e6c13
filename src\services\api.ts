import axios from "axios";
import { createFormDataWithImages } from "@/utils/common";
import { ImageConfig } from "@/types/api";

export const baseURlDEV = "https://ozone-batteries.trentiums.com";
export const baseURlProd = "https://ozonebatteries.com/";
const baseURl = baseURlDEV;
const api = axios.create({
  baseURL: `${baseURl}/api/v1`,
  headers: {
    "Content-Type": "application/json",
  },
});

export const makeMultipartRequest = async (
  endpoint: string,
  payload: any,
  imageConfig?: ImageConfig
) => {
  const hasImages = imageConfig && imageConfig.images.length > 0;

  if (hasImages) {
    const formData = createFormDataWithImages(payload, imageConfig);
    return api.post(endpoint, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  } else {
    return api.post(endpoint, payload);
  }
};

export default api;
