import React from "react";
import { Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { VendorDetails } from "@/types/vendor";
import {
  EntityCard,
  EntityHeader,
  EntityIcon,
  EntityName,
  EntityDetails as EntityDetailsText,
  SearchButton,
  SearchButtonText,
  ManualEntityCard,
  TapToChangeText,
} from "@/styles/Complaint.styles";

interface EntitySelectorProps {
  complaintFor: "vendor" | "customer";
  selectedVendor?: VendorDetails;
  selectedCustomer?: any;
  mobileNumber?: string;
  onSearchPress: () => void;
  onAddManually: () => void;
}

export const EntitySelector: React.FC<EntitySelectorProps> = ({
  complaintFor,
  selectedVendor,
  selectedCustomer,
  mobileNumber,
  onSearchPress,
  onAddManually,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const renderSelectedEntity = () => {
    if (complaintFor === "vendor" && selectedVendor) {
      return (
        <EntityCard>
          <EntityHeader>
            <EntityIcon>
              <Ionicons name="business" size={16} color={theme.colors.white} />
            </EntityIcon>
            <EntityName>{`${selectedVendor.first_name} ${selectedVendor.last_name}`}</EntityName>
          </EntityHeader>
          <EntityDetailsText>{selectedVendor.email}</EntityDetailsText>
        </EntityCard>
      );
    }

    if (complaintFor === "customer" && selectedCustomer) {
      return (
        <EntityCard>
          <EntityHeader>
            <EntityIcon>
              <Ionicons name="person" size={16} color={theme.colors.white} />
            </EntityIcon>
            <EntityName>{selectedCustomer.name}</EntityName>
          </EntityHeader>
          <EntityDetailsText>{selectedCustomer.email}</EntityDetailsText>
        </EntityCard>
      );
    }

    // Show manually entered mobile number
    if (mobileNumber && mobileNumber.trim()) {
      return (
        <ManualEntityCard>
          <EntityHeader>
            <EntityIcon>
              <Ionicons
                name={complaintFor === "vendor" ? "business" : "person"}
                size={16}
                color={theme.colors.white}
              />
            </EntityIcon>
            <EntityName>
              {complaintFor === "vendor"
                ? t("complaint.form.add_vendor_manually")
                : t("complaint.form.add_customer_manually")}
            </EntityName>
            <Ionicons
              name="add-circle"
              size={16}
              color={theme.colors.primary}
              style={{ marginLeft: 8 }}
            />
          </EntityHeader>
          <EntityDetailsText>{mobileNumber}</EntityDetailsText>
        </ManualEntityCard>
      );
    }

    return null;
  };

  return (
    <>
      {/* Show selected entity if any */}
      {renderSelectedEntity()}

      {/* Always show search button */}
      <SearchButton onPress={onSearchPress}>
        <SearchButtonText>
          {selectedVendor ||
          selectedCustomer ||
          (mobileNumber && mobileNumber.trim())
            ? t("complaint.form.change_selection")
            : t("complaint.form.search_existing_add_manually")}
        </SearchButtonText>
        <Ionicons name="search" size={20} color={theme.colors.gray} />
      </SearchButton>

      {/* Show hint when entity is selected */}
      {(selectedVendor ||
        selectedCustomer ||
        (mobileNumber && mobileNumber.trim())) && (
        <TapToChangeText>{t("complaint.form.tap_to_change")}</TapToChangeText>
      )}
    </>
  );
};
