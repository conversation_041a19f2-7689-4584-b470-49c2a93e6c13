import { useState, useEffect, useCallback } from "react";
import * as Location from "expo-location";
import { Alert, Linking, Platform } from "react-native";
import Toast from "react-native-toast-message";
import { useTranslation } from "react-i18next";
import {
  LocationService,
  LocationCoordinates,
  LocationPermissionResult,
} from "@/services/locationService";

export interface LocationState {
  status: Location.PermissionStatus | null;
  isLoading: boolean;
  hasPermission: boolean;
  coordinates: LocationCoordinates | null;
  error: string | null;
}

export interface UseLocationReturn {
  // Permission state
  status: Location.PermissionStatus | null;
  isLoading: boolean;
  hasPermission: boolean;
  coordinates: LocationCoordinates | null;
  error: string | null;

  // Permission methods
  checkPermission: () => Promise<Location.PermissionStatus>;
  requestPermission: () => Promise<Location.PermissionStatus>;
  openSettings: () => void;
  handlePermissionRequest: () => Promise<void>;

  // Location methods
  getCurrentLocation: () => Promise<LocationCoordinates | null>;
  clearError: () => void;
}

export const useLocation = (): UseLocationReturn => {
  const { t } = useTranslation();
  const [state, setState] = useState<LocationState>({
    status: null,
    isLoading: true,
    hasPermission: false,
    coordinates: null,
    error: null,
  });

  const checkPermission = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const { status } = await Location.getForegroundPermissionsAsync();

      setState((prev) => ({
        ...prev,
        status,
        isLoading: false,
        hasPermission: status === Location.PermissionStatus.GRANTED,
      }));

      return status;
    } catch (error) {
      console.error("Error checking location permission:", error);
      setState((prev) => ({
        ...prev,
        status: null,
        isLoading: false,
        hasPermission: false,
        error: "Failed to check location permission",
      }));
      return Location.PermissionStatus.DENIED;
    }
  }, []);

  const requestPermission = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const { status } = await Location.requestForegroundPermissionsAsync();

      setState((prev) => ({
        ...prev,
        status,
        isLoading: false,
        hasPermission: status === Location.PermissionStatus.GRANTED,
      }));

      return status;
    } catch (error) {
      console.error("Error requesting location permission:", error);
      setState((prev) => ({
        ...prev,
        status: null,
        isLoading: false,
        hasPermission: false,
        error: "Failed to request location permission",
      }));
      return Location.PermissionStatus.DENIED;
    }
  }, []);

  const openSettings = useCallback(() => {
    Alert.alert(
      "Location Permission Required",
      "This app needs location access to provide you with nearby services. Please enable location access in your device settings.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Open Settings",
          onPress: () => {
            if (Platform.OS === "ios") {
              Linking.openURL("app-settings:");
            } else {
              Linking.openSettings();
            }
          },
        },
      ]
    );
  }, []);

  const handlePermissionRequest = useCallback(async () => {
    const currentStatus = await checkPermission();

    if (currentStatus === Location.PermissionStatus.UNDETERMINED) {
      const newStatus = await requestPermission();

      if (newStatus === Location.PermissionStatus.DENIED) {
        Toast.show({
          text1: "Location permission denied",
          type: "error",
        });
      }
    } else if (currentStatus === Location.PermissionStatus.DENIED) {
      const newStatus = await requestPermission();

      if (newStatus === Location.PermissionStatus.DENIED) {
        openSettings();
      }
    }
  }, [checkPermission, requestPermission, openSettings]);

  const getCurrentLocation =
    useCallback(async (): Promise<LocationCoordinates | null> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const existingLocation = await LocationService.getCurrentLocation();
        if (existingLocation) {
          setState((prev) => ({
            ...prev,
            coordinates: existingLocation,
            isLoading: false,
          }));
          return existingLocation;
        }

        const result: LocationPermissionResult =
          await LocationService.requestLocationWithPermission();

        if (result.success && result.coordinates) {
          setState((prev) => ({
            ...prev,
            coordinates: result.coordinates,
            isLoading: false,
          }));
          return result.coordinates;
        }

        // Handle different error scenarios
        if (result.permissionStatus === "denied") {
          const errorMessage = t(
            "complaint.location.permission_denied",
            "Location permission denied. Location is required for complaint submission."
          );
          setState((prev) => ({
            ...prev,
            error: errorMessage,
            isLoading: false,
          }));

          LocationService.showLocationSettingsAlert();

          Toast.show({
            type: "error",
            text1: t("error.title", "Error"),
            text2: errorMessage,
          });
        } else if (result.error) {
          setState((prev) => ({
            ...prev,
            error: result.error,
            isLoading: false,
          }));

          Toast.show({
            type: "error",
            text1: t("error.title", "Error"),
            text2: result.error,
          });
        } else {
          const errorMessage = t(
            "complaint.location.unable_to_get_location",
            "Unable to get location coordinates. Please try again."
          );
          setState((prev) => ({
            ...prev,
            error: errorMessage,
            isLoading: false,
          }));

          Toast.show({
            type: "error",
            text1: t("error.title", "Error"),
            text2: errorMessage,
          });
        }

        return null;
      } catch (error) {
        console.error("Error in getCurrentLocation:", error);
        const errorMessage = t(
          "complaint.location.general_error",
          "An error occurred while getting location. Please try again."
        );
        setState((prev) => ({
          ...prev,
          error: errorMessage,
          isLoading: false,
        }));

        Toast.show({
          type: "error",
          text1: t("error.title", "Error"),
          text2: errorMessage,
        });

        return null;
      }
    }, [t]);

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  useEffect(() => {
    checkPermission();
  }, [checkPermission]);

  return {
    // State
    status: state.status,
    isLoading: state.isLoading,
    hasPermission: state.hasPermission,
    coordinates: state.coordinates,
    error: state.error,

    // Methods
    checkPermission,
    requestPermission,
    openSettings,
    handlePermissionRequest,
    getCurrentLocation,
    clearError,
  };
};
